package com.parkingsystem.view;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

import com.parkingsystem.controller.DynamicPricingController;
import com.parkingsystem.controller.FileManager;
import com.parkingsystem.controller.ParkingLotManager;
import com.parkingsystem.controller.QRCodeGenerator;
import com.parkingsystem.controller.SMSController;
import com.parkingsystem.model.Constants;
import com.parkingsystem.model.Vehicle;

/**
 * Panel for handling vehicle entry
 */
public class EntryPanel extends JPanel {
    private ParkingLotManager parkingLotManager;
    private MainFrame mainFrame;

    private JTextField vehicleNumberField;
    private JTextField phoneNumberField;
    private JComboBox<String> vehicleTypeComboBox;
    private JComboBox<String> timeSlotComboBox;
    private JLabel availableTwoWheelerSlotsLabel;
    private JLabel availableFourWheelerSlotsLabel;
    private JLabel pricingStatusLabel;
    private JLabel qrCodeLabel; // Label to display QR code
    private JPanel qrCodePanel; // Panel to hold QR code and related information

    /**
     * Constructor to initialize the entry panel
     *
     * @param parkingLotManager The parking lot manager
     * @param mainFrame The main frame
     */
    public EntryPanel(ParkingLotManager parkingLotManager, MainFrame mainFrame) {
        this.parkingLotManager = parkingLotManager;
        this.mainFrame = mainFrame;

        setLayout(new BorderLayout());

        // Create form panel
        JPanel formPanel = createFormPanel();
        add(formPanel, BorderLayout.NORTH);

        // Create status panel
        JPanel statusPanel = createStatusPanel();
        add(statusPanel, BorderLayout.CENTER);

        // Create QR code panel
        qrCodePanel = createQRCodePanel();
        add(qrCodePanel, BorderLayout.SOUTH);
        qrCodePanel.setVisible(false); // Initially hidden

        // Refresh the panel
        refreshPanel();
    }

    /**
     * Create the form panel for vehicle entry
     *
     * @return The form panel
     */
    private JPanel createFormPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Enter Vehicle Details"));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.fill = GridBagConstraints.HORIZONTAL;

        // Vehicle Number
        gbc.gridx = 0;
        gbc.gridy = 0;
        panel.add(new JLabel("Vehicle Number:"), gbc);

        gbc.gridx = 1;
        gbc.gridy = 0;
        vehicleNumberField = new JTextField(15);
        panel.add(vehicleNumberField, gbc);

        // Phone Number
        gbc.gridx = 0;
        gbc.gridy = 1;
        panel.add(new JLabel("Phone Number:"), gbc);

        gbc.gridx = 1;
        gbc.gridy = 1;
        phoneNumberField = new JTextField(15);
        phoneNumberField.setToolTipText("Enter 10-digit mobile number for SMS notifications");
        panel.add(phoneNumberField, gbc);

        // Vehicle Type
        gbc.gridx = 0;
        gbc.gridy = 2;
        panel.add(new JLabel("Vehicle Type:"), gbc);

        gbc.gridx = 1;
        gbc.gridy = 2;
        vehicleTypeComboBox = new JComboBox<>(new String[] {Constants.TWO_WHEELER, Constants.FOUR_WHEELER});
        panel.add(vehicleTypeComboBox, gbc);

        // Time Slot
        gbc.gridx = 0;
        gbc.gridy = 3;
        gbc.anchor = GridBagConstraints.WEST;
        panel.add(new JLabel("Time Slot:"), gbc);

        gbc.gridx = 1;
        gbc.gridy = 3;
        timeSlotComboBox = new JComboBox<>(getAvailableTimeSlots());
        panel.add(timeSlotComboBox, gbc);

        // Submit Button
        gbc.gridx = 1;
        gbc.gridy = 4;
        gbc.anchor = GridBagConstraints.EAST;
        JButton submitButton = new JButton("Park Vehicle");
        submitButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                parkVehicle();
            }
        });
        panel.add(submitButton, gbc);

        return panel;
    }

    /**
     * Create the status panel showing available slots
     *
     * @return The status panel
     */
    private JPanel createStatusPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Parking Status"));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.fill = GridBagConstraints.HORIZONTAL;

        // Two Wheeler Slots
        gbc.gridx = 0;
        gbc.gridy = 0;
        panel.add(new JLabel("Available Two Wheeler Slots:"), gbc);

        gbc.gridx = 1;
        gbc.gridy = 0;
        availableTwoWheelerSlotsLabel = new JLabel();
        panel.add(availableTwoWheelerSlotsLabel, gbc);

        // Four Wheeler Slots
        gbc.gridx = 0;
        gbc.gridy = 1;
        panel.add(new JLabel("Available Four Wheeler Slots:"), gbc);

        gbc.gridx = 1;
        gbc.gridy = 1;
        availableFourWheelerSlotsLabel = new JLabel();
        panel.add(availableFourWheelerSlotsLabel, gbc);

        // Pricing Status
        gbc.gridx = 0;
        gbc.gridy = 2;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        pricingStatusLabel = new JLabel();
        pricingStatusLabel.setFont(pricingStatusLabel.getFont().deriveFont(12f));
        pricingStatusLabel.setBorder(BorderFactory.createTitledBorder("Current Pricing"));
        panel.add(pricingStatusLabel, gbc);

        return panel;
    }

    /**
     * Get available time slots based on current time and parking closure
     */
    private String[] getAvailableTimeSlots() {
        LocalTime currentTime = LocalTime.now();
        List<String> availableSlots = new ArrayList<>();

        // Check if parking is closed (after 10 PM)
        if (currentTime.getHour() >= Constants.PARKING_CLOSE_HOUR) {
            return new String[]{"Parking Closed - Opens at 06:00"};
        }

        // Add available time slots based on current time
        for (String slot : Constants.TIME_SLOTS) {
            String[] timeParts = slot.split("-");
            if (timeParts.length == 2) {
                try {
                    LocalTime slotEndTime = LocalTime.parse(timeParts[1]);

                    // Only show slots that haven't ended yet
                    if (currentTime.isBefore(slotEndTime)) {
                        availableSlots.add(slot);
                    }
                } catch (Exception e) {
                    // Skip invalid time slots
                }
            }
        }

        if (availableSlots.isEmpty()) {
            return new String[]{"No available time slots"};
        }

        return availableSlots.toArray(new String[0]);
    }

    /**
     * Handle the vehicle parking action
     */
    private void parkVehicle() {
        String vehicleNumber = vehicleNumberField.getText().trim();
        String phoneNumber = phoneNumberField.getText().trim();
        String vehicleType = (String) vehicleTypeComboBox.getSelectedItem();
        String selectedTimeSlot = (String) timeSlotComboBox.getSelectedItem();

        // Validate input
        if (vehicleNumber.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Please enter a vehicle number", "Input Error", JOptionPane.ERROR_MESSAGE);
            return;
        }

        // Validate phone number if provided
        if (!phoneNumber.isEmpty() && !SMSController.isValidPhoneNumber(phoneNumber)) {
            JOptionPane.showMessageDialog(this, "Please enter a valid 10-digit mobile number", "Input Error", JOptionPane.ERROR_MESSAGE);
            return;
        }

        // Check if parking is closed
        if (selectedTimeSlot != null && (selectedTimeSlot.contains("Parking Closed") || selectedTimeSlot.contains("No available"))) {
            JOptionPane.showMessageDialog(this, "Parking is currently closed or no time slots available", "Parking Error", JOptionPane.ERROR_MESSAGE);
            return;
        }

        // Check if vehicle is already parked
        if (parkingLotManager.findVehicle(vehicleNumber) != null) {
            JOptionPane.showMessageDialog(this, "Vehicle with this number is already parked", "Parking Error", JOptionPane.ERROR_MESSAGE);
            return;
        }

        // Create a new vehicle with time slot
        Vehicle vehicle = new Vehicle(vehicleNumber, vehicleType, selectedTimeSlot);

        // Set customer phone number for SMS notifications
        if (!phoneNumber.isEmpty()) {
            vehicle.setCustomerPhone(phoneNumber);
        }

        // Park the vehicle
        int slotNumber = parkingLotManager.parkVehicle(vehicle);

        if (slotNumber != -1) {
            // Log the entry
            FileManager.logEntry(vehicle);

            // Save parking data
            boolean saved = FileManager.saveParkingData(parkingLotManager);
            System.out.println("Parking data saved: " + saved);

            // Show success message with dynamic pricing information
            String pricingInfo = DynamicPricingController.getPricingInfo(vehicle);
            String smsInfo = "";

            if (!phoneNumber.isEmpty()) {
                smsInfo = "\n📱 SMS notifications will be sent to: " + phoneNumber;
            }

            JOptionPane.showMessageDialog(this,
                    "Vehicle parked successfully!\nSlot Number: " + slotNumber +
                    "\nSelected Time Slot: " + selectedTimeSlot + "\n\n" + pricingInfo + smsInfo +
                    "\n\nA QR code for tracking your vehicle will be displayed.",
                    "Parking Success", JOptionPane.INFORMATION_MESSAGE);

            // Send SMS notification
            SMSController.sendEntryConfirmation(vehicle);

            // Generate and display QR code for vehicle tracking
            displayQRCode(vehicle);

            // Clear the form
            vehicleNumberField.setText("");

            // Refresh time slots in case time has changed
            timeSlotComboBox.removeAllItems();
            for (String slot : getAvailableTimeSlots()) {
                timeSlotComboBox.addItem(slot);
            }

            // Refresh all panels (except this one to keep QR code visible)
            mainFrame.refreshOtherPanels(this);
        } else {
            // Show error message
            JOptionPane.showMessageDialog(this,
                    "No available slots for " + vehicleType,
                    "Parking Error", JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * Create the QR code panel for displaying vehicle tracking QR code
     *
     * @return The QR code panel
     */
    private JPanel createQRCodePanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Vehicle Tracking QR Code"));

        // Create a label for the QR code image
        qrCodeLabel = new JLabel();
        qrCodeLabel.setHorizontalAlignment(JLabel.CENTER);
        panel.add(qrCodeLabel, BorderLayout.CENTER);

        // Create a panel for instructions
        JPanel instructionsPanel = new JPanel(new BorderLayout());
        JTextArea instructionsText = new JTextArea(
            "Scan this QR code to track your vehicle online.\n" +
            "You can check parking duration and estimated fees in real-time.\n" +
            "The QR code will be valid until your vehicle exits the parking lot."
        );
        instructionsText.setEditable(false);
        instructionsText.setBackground(panel.getBackground());
        instructionsText.setLineWrap(true);
        instructionsText.setWrapStyleWord(true);
        instructionsPanel.add(instructionsText, BorderLayout.CENTER);

        panel.add(instructionsPanel, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * Display a QR code for the given vehicle
     *
     * @param vehicle The vehicle to display a QR code for
     */
    private void displayQRCode(Vehicle vehicle) {
        try {
            System.out.println("Generating QR code for vehicle: " + vehicle.getVehicleNumber());

            // Generate QR code
            String qrCodePath = QRCodeGenerator.generateQRCode(vehicle);
            System.out.println("QR code generated at: " + qrCodePath);

            // Check if file exists
            java.io.File qrFile = new java.io.File(qrCodePath);
            if (!qrFile.exists()) {
                System.err.println("QR code file does not exist: " + qrCodePath);
                qrCodeLabel.setText("QR Code generation failed");
                qrCodePanel.setVisible(true);
                return;
            }

            // Load and display the QR code image
            ImageIcon qrIcon = new ImageIcon(qrCodePath);

            // Check if image loaded successfully
            if (qrIcon.getIconWidth() <= 0) {
                System.err.println("Failed to load QR code image");
                qrCodeLabel.setText("QR Code image failed to load");
                qrCodePanel.setVisible(true);
                return;
            }

            // Resize if needed
            if (qrIcon.getIconWidth() > 200) {
                qrIcon = new ImageIcon(qrIcon.getImage().getScaledInstance(200, 200, Image.SCALE_SMOOTH));
            }

            qrCodeLabel.setIcon(qrIcon);
            qrCodeLabel.setText(""); // Clear any error text
            qrCodePanel.setVisible(true);

            System.out.println("QR code displayed successfully");

        } catch (Exception e) {
            System.err.println("Error displaying QR code: " + e.getMessage());
            e.printStackTrace();
            qrCodeLabel.setText("QR Code Error: " + e.getMessage());
            qrCodePanel.setVisible(true);
        }
    }

    /**
     * Refresh the panel to update displayed information
     */
    public void refreshPanel() {
        // Update available slots
        availableTwoWheelerSlotsLabel.setText(String.valueOf(parkingLotManager.getAvailableTwoWheelerSlots()));
        availableFourWheelerSlotsLabel.setText(String.valueOf(parkingLotManager.getAvailableFourWheelerSlots()));

        // Update pricing status
        String pricingStatus = DynamicPricingController.getCurrentPricingStatus();
        pricingStatusLabel.setText("<html>" + pricingStatus.replace("\n", "<br>") + "</html>");

        // Refresh time slots in case time has changed
        timeSlotComboBox.removeAllItems();
        for (String slot : getAvailableTimeSlots()) {
            timeSlotComboBox.addItem(slot);
        }

        // Hide QR code panel when refreshing
        qrCodePanel.setVisible(false);
    }
}
