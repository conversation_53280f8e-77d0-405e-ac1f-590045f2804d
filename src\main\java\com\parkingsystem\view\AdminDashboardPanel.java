package com.parkingsystem.view;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import com.parkingsystem.controller.AdminController;
import com.parkingsystem.controller.ParkingLotManager;
import com.parkingsystem.model.Constants;

/**
 * Admin Dashboard Panel for revenue reports and parking statistics
 */
public class AdminDashboardPanel extends JPanel {
    private ParkingLotManager parkingLotManager;
    private MainFrame mainFrame;
    
    private JTextArea statsArea;
    private JTextArea revenueArea;
    private JButton refreshButton;
    private JButton generateReportButton;
    private JButton exportButton;
    private JSpinner dateSpinner;
    private boolean isAuthenticated = false;
    
    /**
     * Constructor to initialize the admin dashboard panel
     *
     * @param parkingLotManager The parking lot manager
     * @param mainFrame The main frame
     */
    public AdminDashboardPanel(ParkingLotManager parkingLotManager, MainFrame mainFrame) {
        this.parkingLotManager = parkingLotManager;
        this.mainFrame = mainFrame;
        
        setLayout(new BorderLayout());
        
        // Check authentication first
        if (!authenticateAdmin()) {
            showAccessDenied();
            return;
        }
        
        isAuthenticated = true;
        initializeDashboard();
    }
    
    /**
     * Authenticate admin user
     */
    private boolean authenticateAdmin() {
        JPanel loginPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        
        JTextField usernameField = new JTextField(15);
        JPasswordField passwordField = new JPasswordField(15);
        
        gbc.gridx = 0; gbc.gridy = 0;
        loginPanel.add(new JLabel("Username:"), gbc);
        gbc.gridx = 1;
        loginPanel.add(usernameField, gbc);
        
        gbc.gridx = 0; gbc.gridy = 1;
        loginPanel.add(new JLabel("Password:"), gbc);
        gbc.gridx = 1;
        loginPanel.add(passwordField, gbc);
        
        int result = JOptionPane.showConfirmDialog(
            this, loginPanel, "Admin Login", JOptionPane.OK_CANCEL_OPTION, JOptionPane.PLAIN_MESSAGE
        );
        
        if (result == JOptionPane.OK_OPTION) {
            String username = usernameField.getText();
            String password = new String(passwordField.getPassword());
            return AdminController.authenticateAdmin(username, password);
        }
        
        return false;
    }
    
    /**
     * Show access denied message
     */
    private void showAccessDenied() {
        setLayout(new BorderLayout());
        JLabel accessDeniedLabel = new JLabel("Access Denied - Admin Authentication Required", JLabel.CENTER);
        accessDeniedLabel.setFont(accessDeniedLabel.getFont().deriveFont(16f));
        accessDeniedLabel.setForeground(Color.RED);
        add(accessDeniedLabel, BorderLayout.CENTER);
        
        JButton retryButton = new JButton("Retry Login");
        retryButton.addActionListener(e -> {
            removeAll();
            if (authenticateAdmin()) {
                isAuthenticated = true;
                initializeDashboard();
            } else {
                showAccessDenied();
            }
            revalidate();
            repaint();
        });
        
        JPanel buttonPanel = new JPanel();
        buttonPanel.add(retryButton);
        add(buttonPanel, BorderLayout.SOUTH);
    }
    
    /**
     * Initialize the dashboard components
     */
    private void initializeDashboard() {
        removeAll();
        
        // Create header panel
        JPanel headerPanel = createHeaderPanel();
        add(headerPanel, BorderLayout.NORTH);
        
        // Create main content panel
        JPanel contentPanel = createContentPanel();
        add(contentPanel, BorderLayout.CENTER);
        
        // Create control panel
        JPanel controlPanel = createControlPanel();
        add(controlPanel, BorderLayout.SOUTH);
        
        // Initial data load
        refreshDashboard();
    }
    
    /**
     * Create header panel
     */
    private JPanel createHeaderPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Admin Dashboard"));
        
        JLabel titleLabel = new JLabel("Smart Parking System - Admin Dashboard", JLabel.CENTER);
        titleLabel.setFont(titleLabel.getFont().deriveFont(18f));
        panel.add(titleLabel, BorderLayout.CENTER);
        
        JLabel timeLabel = new JLabel("Last Updated: " + LocalDate.now().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
        panel.add(timeLabel, BorderLayout.EAST);
        
        return panel;
    }
    
    /**
     * Create main content panel
     */
    private JPanel createContentPanel() {
        JPanel panel = new JPanel(new GridLayout(1, 2, 10, 10));
        panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        // Current Statistics Panel
        JPanel statsPanel = new JPanel(new BorderLayout());
        statsPanel.setBorder(BorderFactory.createTitledBorder("Current Parking Statistics"));
        
        statsArea = new JTextArea(15, 30);
        statsArea.setEditable(false);
        statsArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        JScrollPane statsScroll = new JScrollPane(statsArea);
        statsPanel.add(statsScroll, BorderLayout.CENTER);
        
        // Revenue Report Panel
        JPanel revenuePanel = new JPanel(new BorderLayout());
        revenuePanel.setBorder(BorderFactory.createTitledBorder("Daily Revenue Report"));
        
        revenueArea = new JTextArea(15, 30);
        revenueArea.setEditable(false);
        revenueArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        JScrollPane revenueScroll = new JScrollPane(revenueArea);
        revenuePanel.add(revenueScroll, BorderLayout.CENTER);
        
        panel.add(statsPanel);
        panel.add(revenuePanel);
        
        return panel;
    }
    
    /**
     * Create control panel
     */
    private JPanel createControlPanel() {
        JPanel panel = new JPanel(new FlowLayout());
        
        // Date selector
        panel.add(new JLabel("Report Date:"));
        dateSpinner = new JSpinner(new SpinnerDateModel());
        JSpinner.DateEditor dateEditor = new JSpinner.DateEditor(dateSpinner, "dd/MM/yyyy");
        dateSpinner.setEditor(dateEditor);
        dateSpinner.setValue(java.util.Date.from(LocalDate.now().atStartOfDay().atZone(java.time.ZoneId.systemDefault()).toInstant()));
        panel.add(dateSpinner);
        
        // Buttons
        refreshButton = new JButton("Refresh");
        refreshButton.addActionListener(e -> refreshDashboard());
        panel.add(refreshButton);
        
        generateReportButton = new JButton("Generate Report");
        generateReportButton.addActionListener(e -> generateReport());
        panel.add(generateReportButton);
        
        exportButton = new JButton("Export CSV");
        exportButton.addActionListener(e -> exportReport());
        panel.add(exportButton);
        
        return panel;
    }
    
    /**
     * Refresh dashboard data
     */
    private void refreshDashboard() {
        if (!isAuthenticated) return;
        
        // Update current statistics
        AdminController.ParkingStats stats = AdminController.getCurrentStats(parkingLotManager);
        updateStatsDisplay(stats);
        
        // Update revenue report for today
        LocalDate today = LocalDate.now();
        AdminController.RevenueReport report = AdminController.generateDailyReport(today);
        updateRevenueDisplay(report);
    }
    
    /**
     * Update statistics display
     */
    private void updateStatsDisplay(AdminController.ParkingStats stats) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== CURRENT PARKING STATISTICS ===\n\n");
        sb.append(String.format("Total Slots: %d\n", stats.getTotalSlots()));
        sb.append(String.format("Occupied Slots: %d\n", stats.getOccupiedSlots()));
        sb.append(String.format("Available Slots: %d\n", stats.getAvailableSlots()));
        sb.append(String.format("Occupancy Rate: %.1f%%\n\n", stats.getOccupancyRate()));
        
        sb.append("=== VEHICLE BREAKDOWN ===\n");
        sb.append(String.format("Two Wheelers: %d\n", stats.getTwoWheelerOccupied()));
        sb.append(String.format("Four Wheelers: %d\n", stats.getFourWheelerOccupied()));
        sb.append(String.format("Peak Hour Parking: %d\n\n", stats.getPeakHourParking()));
        
        sb.append("=== SLOT AVAILABILITY ===\n");
        sb.append(String.format("Two Wheeler Slots: %d/%d\n", 
            stats.getTwoWheelerOccupied(), Constants.DEFAULT_TWO_WHEELER_SLOTS));
        sb.append(String.format("Four Wheeler Slots: %d/%d\n", 
            stats.getFourWheelerOccupied(), Constants.DEFAULT_FOUR_WHEELER_SLOTS));
        
        statsArea.setText(sb.toString());
    }
    
    /**
     * Update revenue display
     */
    private void updateRevenueDisplay(AdminController.RevenueReport report) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== DAILY REVENUE REPORT ===\n\n");
        sb.append(String.format("Date: %s\n\n", report.getDate().format(DateTimeFormatter.ofPattern("dd/MM/yyyy"))));
        sb.append(String.format("Total Revenue: ₹%.2f\n", report.getTotalRevenue()));
        sb.append(String.format("Total Vehicles: %d\n\n", report.getTotalVehicles()));
        
        sb.append("=== VEHICLE BREAKDOWN ===\n");
        sb.append(String.format("Two Wheelers: %d\n", report.getTwoWheelers()));
        sb.append(String.format("Four Wheelers: %d\n\n", report.getFourWheelers()));
        
        if (report.getTotalVehicles() > 0) {
            double avgRevenue = report.getTotalRevenue() / report.getTotalVehicles();
            sb.append(String.format("Average Revenue per Vehicle: ₹%.2f\n", avgRevenue));
        }
        
        revenueArea.setText(sb.toString());
    }
    
    /**
     * Generate report for selected date
     */
    private void generateReport() {
        java.util.Date selectedDate = (java.util.Date) dateSpinner.getValue();
        LocalDate reportDate = selectedDate.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
        
        AdminController.RevenueReport report = AdminController.generateDailyReport(reportDate);
        updateRevenueDisplay(report);
    }
    
    /**
     * Export report to CSV
     */
    private void exportReport() {
        java.util.Date selectedDate = (java.util.Date) dateSpinner.getValue();
        LocalDate reportDate = selectedDate.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
        
        AdminController.RevenueReport report = AdminController.generateDailyReport(reportDate);
        String filename = "revenue_report_" + reportDate.format(DateTimeFormatter.ofPattern("yyyy_MM_dd")) + ".csv";
        
        boolean success = AdminController.exportReportToCSV(report, filename);
        
        if (success) {
            JOptionPane.showMessageDialog(this, 
                "Report exported successfully to: " + Constants.REPORTS_DIRECTORY + "/" + filename,
                "Export Success", JOptionPane.INFORMATION_MESSAGE);
        } else {
            JOptionPane.showMessageDialog(this, 
                "Failed to export report. Please check file permissions.",
                "Export Error", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * Refresh the panel
     */
    public void refreshPanel() {
        if (isAuthenticated) {
            refreshDashboard();
        }
    }
}
