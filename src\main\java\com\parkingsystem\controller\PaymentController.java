package com.parkingsystem.controller;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;

import javax.swing.JOptionPane;

import com.parkingsystem.model.Constants;
import com.parkingsystem.model.Vehicle;

/**
 * Controller for handling different payment methods
 */
public class PaymentController {
    
    private static final Random random = new Random();
    
    /**
     * Process payment for a vehicle
     *
     * @param vehicle The vehicle to process payment for
     * @param totalFee The total fee to charge
     * @param paymentMethod The selected payment method
     * @return Payment result with transaction details
     */
    public static PaymentResult processPayment(Vehicle vehicle, double totalFee, String paymentMethod) {
        vehicle.setPaymentMethod(paymentMethod);
        
        switch (paymentMethod) {
            case "Cash":
                return processCashPayment(totalFee);
            case "UPI":
                return processUPIPayment(totalFee);
            case "Credit Card":
            case "Debit Card":
                return processCardPayment(totalFee, paymentMethod);
            case "Digital Wallet":
                return processWalletPayment(totalFee);
            default:
                return new PaymentResult(false, "Invalid payment method", null);
        }
    }
    
    /**
     * Process cash payment
     *
     * @param totalFee The total fee
     * @return Payment result
     */
    private static PaymentResult processCashPayment(double totalFee) {
        String amountStr = JOptionPane.showInputDialog(
            null,
            "Total Amount: ₹" + String.format("%.2f", totalFee) + "\n\nEnter cash received:",
            "Cash Payment",
            JOptionPane.QUESTION_MESSAGE
        );
        
        if (amountStr == null) {
            return new PaymentResult(false, "Payment cancelled", null);
        }
        
        try {
            double cashReceived = Double.parseDouble(amountStr);
            
            if (cashReceived < totalFee) {
                return new PaymentResult(false, "Insufficient cash amount", null);
            }
            
            double change = cashReceived - totalFee;
            String transactionId = generateTransactionId("CASH");
            
            String details = String.format(
                "Cash Payment Successful\nTransaction ID: %s\nAmount Paid: ₹%.2f\nChange: ₹%.2f",
                transactionId, cashReceived, change
            );
            
            return new PaymentResult(true, details, transactionId);
            
        } catch (NumberFormatException e) {
            return new PaymentResult(false, "Invalid amount entered", null);
        }
    }
    
    /**
     * Process UPI payment
     *
     * @param totalFee The total fee
     * @return Payment result
     */
    private static PaymentResult processUPIPayment(double totalFee) {
        String[] upiOptions = Constants.UPI_APPS;
        String selectedApp = (String) JOptionPane.showInputDialog(
            null,
            "Select UPI App:",
            "UPI Payment - ₹" + String.format("%.2f", totalFee),
            JOptionPane.QUESTION_MESSAGE,
            null,
            upiOptions,
            upiOptions[0]
        );
        
        if (selectedApp == null) {
            return new PaymentResult(false, "Payment cancelled", null);
        }
        
        // Simulate UPI payment process
        int result = JOptionPane.showConfirmDialog(
            null,
            "Please complete payment on your " + selectedApp + " app\n" +
            "Amount: ₹" + String.format("%.2f", totalFee) + "\n\n" +
            "Click OK after successful payment, Cancel to abort",
            "UPI Payment",
            JOptionPane.OK_CANCEL_OPTION
        );
        
        if (result == JOptionPane.OK_OPTION) {
            String transactionId = generateTransactionId("UPI");
            String details = String.format(
                "UPI Payment Successful\nApp: %s\nTransaction ID: %s\nAmount: ₹%.2f",
                selectedApp, transactionId, totalFee
            );
            return new PaymentResult(true, details, transactionId);
        } else {
            return new PaymentResult(false, "UPI payment cancelled", null);
        }
    }
    
    /**
     * Process card payment
     *
     * @param totalFee The total fee
     * @param cardType The card type (Credit/Debit)
     * @return Payment result
     */
    private static PaymentResult processCardPayment(double totalFee, String cardType) {
        String cardNumber = JOptionPane.showInputDialog(
            null,
            "Enter last 4 digits of " + cardType.toLowerCase() + " card:",
            cardType + " Payment - ₹" + String.format("%.2f", totalFee),
            JOptionPane.QUESTION_MESSAGE
        );
        
        if (cardNumber == null) {
            return new PaymentResult(false, "Payment cancelled", null);
        }
        
        if (!cardNumber.matches("\\d{4}")) {
            return new PaymentResult(false, "Invalid card number format", null);
        }
        
        // Simulate card processing
        int result = JOptionPane.showConfirmDialog(
            null,
            "Processing " + cardType + " payment...\n" +
            "Card ending in: " + cardNumber + "\n" +
            "Amount: ₹" + String.format("%.2f", totalFee) + "\n\n" +
            "Confirm payment?",
            cardType + " Payment",
            JOptionPane.YES_NO_OPTION
        );
        
        if (result == JOptionPane.YES_OPTION) {
            String transactionId = generateTransactionId("CARD");
            String details = String.format(
                "%s Payment Successful\nCard: ****%s\nTransaction ID: %s\nAmount: ₹%.2f",
                cardType, cardNumber, transactionId, totalFee
            );
            return new PaymentResult(true, details, transactionId);
        } else {
            return new PaymentResult(false, cardType + " payment cancelled", null);
        }
    }
    
    /**
     * Process digital wallet payment
     *
     * @param totalFee The total fee
     * @return Payment result
     */
    private static PaymentResult processWalletPayment(double totalFee) {
        String walletId = JOptionPane.showInputDialog(
            null,
            "Enter wallet ID or phone number:",
            "Digital Wallet Payment - ₹" + String.format("%.2f", totalFee),
            JOptionPane.QUESTION_MESSAGE
        );
        
        if (walletId == null) {
            return new PaymentResult(false, "Payment cancelled", null);
        }
        
        // Simulate wallet payment
        int result = JOptionPane.showConfirmDialog(
            null,
            "Processing wallet payment...\n" +
            "Wallet ID: " + walletId + "\n" +
            "Amount: ₹" + String.format("%.2f", totalFee) + "\n\n" +
            "Confirm payment?",
            "Wallet Payment",
            JOptionPane.YES_NO_OPTION
        );
        
        if (result == JOptionPane.YES_OPTION) {
            String transactionId = generateTransactionId("WALLET");
            String details = String.format(
                "Wallet Payment Successful\nWallet ID: %s\nTransaction ID: %s\nAmount: ₹%.2f",
                walletId, transactionId, totalFee
            );
            return new PaymentResult(true, details, transactionId);
        } else {
            return new PaymentResult(false, "Wallet payment cancelled", null);
        }
    }
    
    /**
     * Generate a unique transaction ID
     *
     * @param prefix The transaction prefix
     * @return The generated transaction ID
     */
    private static String generateTransactionId(String prefix) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        String timestamp = LocalDateTime.now().format(formatter);
        int randomNum = random.nextInt(9999);
        return String.format("%s%s%04d", prefix, timestamp, randomNum);
    }
    
    /**
     * Payment result class
     */
    public static class PaymentResult {
        private final boolean success;
        private final String message;
        private final String transactionId;
        
        public PaymentResult(boolean success, String message, String transactionId) {
            this.success = success;
            this.message = message;
            this.transactionId = transactionId;
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public String getMessage() {
            return message;
        }
        
        public String getTransactionId() {
            return transactionId;
        }
    }
}
