package com.parkingsystem.controller;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;

import com.parkingsystem.model.Constants;
import com.parkingsystem.model.Vehicle;

/**
 * Calculates parking fees based on vehicle type and duration
 */
public class FeeCalculator {
    
    /**
     * Calculate the parking fee for a vehicle
     *
     * @param vehicle The vehicle for which to calculate the fee
     * @return The calculated fee
     */
    public static double calculateFee(Vehicle vehicle) {
        // Use dynamic pricing for all vehicles
        double baseFee = DynamicPricingController.calculateDynamicFee(vehicle);

        // Add overtime/overstay fines
        double overtimeFine = getOvertimeFine(vehicle);
        double overstayFine = calculateOverstayFine(vehicle);

        return baseFee + overtimeFine + overstayFine;
    }

    /**
     * Calculate fee using the new time-slot-based pricing system
     *
     * @param vehicle The vehicle for which to calculate the fee
     * @return The calculated fee
     */
    private static double calculateTimeSlotBasedFee(Vehicle vehicle) {
        double baseFee;

        // Calculate base fee for the selected time slot
        if (vehicle.getVehicleType().equals(Constants.TWO_WHEELER)) {
            baseFee = Constants.TWO_WHEELER_SLOT_RATE;
        } else {
            baseFee = Constants.FOUR_WHEELER_SLOT_RATE;
        }

        // Check if vehicle exceeded the selected time slot
        double overtimeFine = calculateOvertimeFine(vehicle);

        return baseFee + overtimeFine;
    }

    /**
     * Get overtime fine amount for display purposes
     *
     * @param vehicle The vehicle to check for overtime
     * @return The overtime fine amount
     */
    public static double getOvertimeFine(Vehicle vehicle) {
        if (vehicle.getSelectedTimeSlot() != null && !vehicle.getSelectedTimeSlot().isEmpty()) {
            return calculateOvertimeFine(vehicle);
        }
        return 0.0;
    }

    /**
     * Calculate fee using the legacy hourly pricing system
     *
     * @param vehicle The vehicle for which to calculate the fee
     * @return The calculated fee
     */
    private static double calculateLegacyFee(Vehicle vehicle) {
        LocalDateTime entryTime = vehicle.getEntryTime();
        LocalDateTime exitTime = vehicle.getExitTime() != null ? vehicle.getExitTime() : LocalDateTime.now();

        // Calculate duration in hours
        Duration duration = Duration.between(entryTime, exitTime);
        double hours = duration.toMinutes() / 60.0;

        double baseFee = 0.0;

        // Apply minimum fee for less than an hour
        if (hours < 1.0) {
            if (vehicle.getVehicleType().equals(Constants.TWO_WHEELER)) {
                baseFee = Constants.TWO_WHEELER_MIN_FEE;
            } else {
                baseFee = Constants.FOUR_WHEELER_MIN_FEE;
            }
        } else {
            // Calculate fee based on vehicle type and duration
            if (vehicle.getVehicleType().equals(Constants.TWO_WHEELER)) {
                baseFee = Math.ceil(hours) * Constants.TWO_WHEELER_RATE;
            } else {
                baseFee = Math.ceil(hours) * Constants.FOUR_WHEELER_RATE;
            }
        }

        // Add overstay fine if applicable
        double overstayFine = calculateOverstayFine(vehicle);

        return baseFee + overstayFine;
    }

    /**
     * Calculate overtime fine for vehicles with selected time slots
     *
     * @param vehicle The vehicle to check for overtime
     * @return The overtime fine amount
     */
    private static double calculateOvertimeFine(Vehicle vehicle) {
        if (vehicle.getSelectedTimeSlot() == null || vehicle.getSelectedTimeSlot().isEmpty()) {
            return 0.0;
        }

        LocalDateTime exitTime = vehicle.getExitTime() != null ? vehicle.getExitTime() : LocalDateTime.now();

        // Parse the selected time slot to get end time
        String[] timeParts = vehicle.getSelectedTimeSlot().split("-");
        if (timeParts.length != 2) {
            return 0.0; // Invalid time slot format
        }

        try {
            String endTimeStr = timeParts[1];
            LocalTime slotEndTime = LocalTime.parse(endTimeStr);

            // Create the slot end datetime for the same date as entry
            LocalDateTime slotEndDateTime = vehicle.getEntryTime().toLocalDate().atTime(slotEndTime);

            // If the slot end time is before entry time (next day scenario), add one day
            if (slotEndTime.isBefore(vehicle.getEntryTime().toLocalTime())) {
                slotEndDateTime = slotEndDateTime.plusDays(1);
            }

            // If exit time is after slot end time, apply overtime fine
            if (exitTime.isAfter(slotEndDateTime)) {
                return Constants.OVERTIME_FINE;
            }
        } catch (Exception e) {
            // If parsing fails, no fine
            return 0.0;
        }

        return 0.0;
    }

    /**
     * Calculate overstay fine for a vehicle (legacy system)
     *
     * @param vehicle The vehicle to check for overstay
     * @return The overstay fine amount
     */
    public static double calculateOverstayFine(Vehicle vehicle) {
        LocalDateTime entryTime = vehicle.getEntryTime();
        LocalDateTime exitTime = vehicle.getExitTime() != null ? vehicle.getExitTime() : LocalDateTime.now();

        // Calculate duration in hours
        Duration duration = Duration.between(entryTime, exitTime);
        double hours = duration.toMinutes() / 60.0;

        // Check if vehicle has overstayed
        if (hours > Constants.MAX_PARKING_HOURS) {
            double overstayHours = hours - Constants.MAX_PARKING_HOURS;
            return Constants.OVERSTAY_BASE_FINE + (Math.ceil(overstayHours) * Constants.OVERSTAY_FINE_PER_HOUR);
        }

        return 0.0;
    }

    /**
     * Check if a vehicle has overstayed the time limit
     *
     * @param vehicle The vehicle to check
     * @return true if the vehicle has overstayed, false otherwise
     */
    public static boolean isOverstayed(Vehicle vehicle) {
        LocalDateTime entryTime = vehicle.getEntryTime();
        LocalDateTime currentTime = LocalDateTime.now();

        Duration duration = Duration.between(entryTime, currentTime);
        double hours = duration.toMinutes() / 60.0;

        return hours > Constants.MAX_PARKING_HOURS;
    }
    
    /**
     * Format the duration between entry and exit times
     * 
     * @param vehicle The vehicle
     * @return Formatted duration string (e.g., "2 hours 30 minutes")
     */
    public static String formatDuration(Vehicle vehicle) {
        LocalDateTime entryTime = vehicle.getEntryTime();
        LocalDateTime exitTime = vehicle.getExitTime() != null ? vehicle.getExitTime() : LocalDateTime.now();
        
        Duration duration = Duration.between(entryTime, exitTime);
        long hours = duration.toHours();
        long minutes = duration.toMinutes() % 60;
        
        if (hours > 0) {
            return hours + " hour" + (hours != 1 ? "s" : "") + 
                   (minutes > 0 ? " " + minutes + " minute" + (minutes != 1 ? "s" : "") : "");
        } else {
            return minutes + " minute" + (minutes != 1 ? "s" : "");
        }
    }
}
