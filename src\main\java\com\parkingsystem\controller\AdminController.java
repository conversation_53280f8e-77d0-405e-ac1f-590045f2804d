package com.parkingsystem.controller;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.parkingsystem.model.Constants;
import com.parkingsystem.model.Vehicle;

/**
 * Controller for admin dashboard functionality
 */
public class AdminController {
    
    /**
     * Authenticate admin user
     *
     * @param username The username
     * @param password The password
     * @return True if authentication successful
     */
    public static boolean authenticateAdmin(String username, String password) {
        return Constants.ADMIN_USERNAME.equals(username) && Constants.ADMIN_PASSWORD.equals(password);
    }
    
    /**
     * Generate daily revenue report
     *
     * @param date The date for the report
     * @return Revenue report data
     */
    public static RevenueReport generateDailyReport(LocalDate date) {
        List<String> transactions = FileManager.getTransactionLogs();
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String targetDate = date.format(dateFormatter);
        
        double totalRevenue = 0.0;
        int totalVehicles = 0;
        int twoWheelers = 0;
        int fourWheelers = 0;
        double peakHourRevenue = 0.0;
        double weekendRevenue = 0.0;
        Map<String, Double> paymentMethodRevenue = new HashMap<>();
        
        for (String transaction : transactions) {
            String[] parts = transaction.split(" \\| ");
            if (parts.length >= 5) {
                String transactionDate = parts[0].split(" ")[0];
                String type = parts[1];
                String vehicleType = parts[3];
                double fee = Double.parseDouble(parts[4]);
                
                if (transactionDate.equals(targetDate) && "EXIT".equals(type)) {
                    totalRevenue += fee;
                    totalVehicles++;
                    
                    if (Constants.TWO_WHEELER.equals(vehicleType)) {
                        twoWheelers++;
                    } else {
                        fourWheelers++;
                    }
                    
                    // Note: For detailed analysis, we'd need to store more data in transaction logs
                    // This is a simplified version
                }
            }
        }
        
        return new RevenueReport(date, totalRevenue, totalVehicles, twoWheelers, fourWheelers, 
                               peakHourRevenue, weekendRevenue, paymentMethodRevenue);
    }
    
    /**
     * Generate monthly revenue report
     *
     * @param year The year
     * @param month The month
     * @return Monthly revenue report
     */
    public static MonthlyReport generateMonthlyReport(int year, int month) {
        List<RevenueReport> dailyReports = new ArrayList<>();
        double totalRevenue = 0.0;
        int totalVehicles = 0;
        
        // Generate daily reports for the month
        LocalDate startDate = LocalDate.of(year, month, 1);
        LocalDate endDate = startDate.withDayOfMonth(startDate.lengthOfMonth());
        
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            RevenueReport dailyReport = generateDailyReport(date);
            dailyReports.add(dailyReport);
            totalRevenue += dailyReport.getTotalRevenue();
            totalVehicles += dailyReport.getTotalVehicles();
        }
        
        return new MonthlyReport(year, month, dailyReports, totalRevenue, totalVehicles);
    }
    
    /**
     * Get current parking statistics
     *
     * @param parkingLotManager The parking lot manager
     * @return Current parking statistics
     */
    public static ParkingStats getCurrentStats(com.parkingsystem.controller.ParkingLotManager parkingLotManager) {
        int totalSlots = Constants.DEFAULT_TWO_WHEELER_SLOTS + Constants.DEFAULT_FOUR_WHEELER_SLOTS;
        int occupiedSlots = parkingLotManager.getParkedVehicles().size();
        int availableSlots = totalSlots - occupiedSlots;
        double occupancyRate = (double) occupiedSlots / totalSlots * 100;
        
        int twoWheelerOccupied = 0;
        int fourWheelerOccupied = 0;
        int peakHourParking = 0;
        
        for (Vehicle vehicle : parkingLotManager.getParkedVehicles().values()) {
            if (Constants.TWO_WHEELER.equals(vehicle.getVehicleType())) {
                twoWheelerOccupied++;
            } else {
                fourWheelerOccupied++;
            }
            
            if (DynamicPricingController.isPeakHour(vehicle.getEntryTime())) {
                peakHourParking++;
            }
        }
        
        return new ParkingStats(totalSlots, occupiedSlots, availableSlots, occupancyRate,
                              twoWheelerOccupied, fourWheelerOccupied, peakHourParking);
    }
    
    /**
     * Export report to CSV file
     *
     * @param report The report to export
     * @param filename The filename
     * @return True if export successful
     */
    public static boolean exportReportToCSV(RevenueReport report, String filename) {
        try {
            // Create reports directory if it doesn't exist
            File reportsDir = new File(Constants.REPORTS_DIRECTORY);
            if (!reportsDir.exists()) {
                reportsDir.mkdirs();
            }
            
            File file = new File(reportsDir, filename);
            try (FileWriter writer = new FileWriter(file)) {
                writer.write("Date,Total Revenue,Total Vehicles,Two Wheelers,Four Wheelers\n");
                writer.write(String.format("%s,%.2f,%d,%d,%d\n",
                    report.getDate().toString(),
                    report.getTotalRevenue(),
                    report.getTotalVehicles(),
                    report.getTwoWheelers(),
                    report.getFourWheelers()
                ));
            }
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Revenue Report class
     */
    public static class RevenueReport {
        private final LocalDate date;
        private final double totalRevenue;
        private final int totalVehicles;
        private final int twoWheelers;
        private final int fourWheelers;
        private final double peakHourRevenue;
        private final double weekendRevenue;
        private final Map<String, Double> paymentMethodRevenue;
        
        public RevenueReport(LocalDate date, double totalRevenue, int totalVehicles, 
                           int twoWheelers, int fourWheelers, double peakHourRevenue, 
                           double weekendRevenue, Map<String, Double> paymentMethodRevenue) {
            this.date = date;
            this.totalRevenue = totalRevenue;
            this.totalVehicles = totalVehicles;
            this.twoWheelers = twoWheelers;
            this.fourWheelers = fourWheelers;
            this.peakHourRevenue = peakHourRevenue;
            this.weekendRevenue = weekendRevenue;
            this.paymentMethodRevenue = paymentMethodRevenue;
        }
        
        // Getters
        public LocalDate getDate() { return date; }
        public double getTotalRevenue() { return totalRevenue; }
        public int getTotalVehicles() { return totalVehicles; }
        public int getTwoWheelers() { return twoWheelers; }
        public int getFourWheelers() { return fourWheelers; }
        public double getPeakHourRevenue() { return peakHourRevenue; }
        public double getWeekendRevenue() { return weekendRevenue; }
        public Map<String, Double> getPaymentMethodRevenue() { return paymentMethodRevenue; }
    }
    
    /**
     * Monthly Report class
     */
    public static class MonthlyReport {
        private final int year;
        private final int month;
        private final List<RevenueReport> dailyReports;
        private final double totalRevenue;
        private final int totalVehicles;
        
        public MonthlyReport(int year, int month, List<RevenueReport> dailyReports, 
                           double totalRevenue, int totalVehicles) {
            this.year = year;
            this.month = month;
            this.dailyReports = dailyReports;
            this.totalRevenue = totalRevenue;
            this.totalVehicles = totalVehicles;
        }
        
        // Getters
        public int getYear() { return year; }
        public int getMonth() { return month; }
        public List<RevenueReport> getDailyReports() { return dailyReports; }
        public double getTotalRevenue() { return totalRevenue; }
        public int getTotalVehicles() { return totalVehicles; }
    }
    
    /**
     * Parking Statistics class
     */
    public static class ParkingStats {
        private final int totalSlots;
        private final int occupiedSlots;
        private final int availableSlots;
        private final double occupancyRate;
        private final int twoWheelerOccupied;
        private final int fourWheelerOccupied;
        private final int peakHourParking;
        
        public ParkingStats(int totalSlots, int occupiedSlots, int availableSlots, 
                          double occupancyRate, int twoWheelerOccupied, 
                          int fourWheelerOccupied, int peakHourParking) {
            this.totalSlots = totalSlots;
            this.occupiedSlots = occupiedSlots;
            this.availableSlots = availableSlots;
            this.occupancyRate = occupancyRate;
            this.twoWheelerOccupied = twoWheelerOccupied;
            this.fourWheelerOccupied = fourWheelerOccupied;
            this.peakHourParking = peakHourParking;
        }
        
        // Getters
        public int getTotalSlots() { return totalSlots; }
        public int getOccupiedSlots() { return occupiedSlots; }
        public int getAvailableSlots() { return availableSlots; }
        public double getOccupancyRate() { return occupancyRate; }
        public int getTwoWheelerOccupied() { return twoWheelerOccupied; }
        public int getFourWheelerOccupied() { return fourWheelerOccupied; }
        public int getPeakHourParking() { return peakHourParking; }
    }
}
