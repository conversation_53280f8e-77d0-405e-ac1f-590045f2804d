package com.parkingsystem.view;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import com.parkingsystem.controller.FeeCalculator;
import com.parkingsystem.controller.FileManager;
import com.parkingsystem.controller.ParkingLotManager;
import com.parkingsystem.model.Vehicle;

/**
 * Panel for handling vehicle exit
 */
public class ExitPanel extends JPanel {
    private ParkingLotManager parkingLotManager;
    private MainFrame mainFrame;

    private JTextField vehicleNumberField;
    private JButton findButton;
    private JButton exitButton;

    private JLabel vehicleTypeLabel;
    private JLabel entryTimeLabel;
    private JLabel slotNumberLabel;
    private JLabel durationLabel;
    private JLabel feeLabel;

    private Vehicle currentVehicle;

    /**
     * Constructor to initialize the exit panel
     *
     * @param parkingLotManager The parking lot manager
     * @param mainFrame The main frame
     */
    public ExitPanel(ParkingLotManager parkingLotManager, MainFrame mainFrame) {
        this.parkingLotManager = parkingLotManager;
        this.mainFrame = mainFrame;

        setLayout(new BorderLayout());

        // Create search panel
        JPanel searchPanel = createSearchPanel();
        add(searchPanel, BorderLayout.NORTH);

        // Create details panel
        JPanel detailsPanel = createDetailsPanel();
        add(detailsPanel, BorderLayout.CENTER);

        // Initialize labels
        clearVehicleDetails();

        // Disable exit button initially
        exitButton.setEnabled(false);
    }

    /**
     * Create the search panel for finding vehicles
     *
     * @return The search panel
     */
    private JPanel createSearchPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Find Vehicle"));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.fill = GridBagConstraints.HORIZONTAL;

        // Vehicle Number
        gbc.gridx = 0;
        gbc.gridy = 0;
        panel.add(new JLabel("Vehicle Number:"), gbc);

        gbc.gridx = 1;
        gbc.gridy = 0;
        vehicleNumberField = new JTextField(15);
        panel.add(vehicleNumberField, gbc);

        // Find Button
        gbc.gridx = 2;
        gbc.gridy = 0;
        findButton = new JButton("Find");
        findButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                findVehicle();
            }
        });
        panel.add(findButton, gbc);

        return panel;
    }

    /**
     * Create the details panel for showing vehicle information
     *
     * @return The details panel
     */
    private JPanel createDetailsPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Vehicle Details"));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.anchor = GridBagConstraints.WEST;

        // Vehicle Type
        gbc.gridx = 0;
        gbc.gridy = 0;
        panel.add(new JLabel("Vehicle Type:"), gbc);

        gbc.gridx = 1;
        gbc.gridy = 0;
        vehicleTypeLabel = new JLabel();
        panel.add(vehicleTypeLabel, gbc);

        // Entry Time
        gbc.gridx = 0;
        gbc.gridy = 1;
        panel.add(new JLabel("Entry Time:"), gbc);

        gbc.gridx = 1;
        gbc.gridy = 1;
        entryTimeLabel = new JLabel();
        panel.add(entryTimeLabel, gbc);

        // Slot Number
        gbc.gridx = 0;
        gbc.gridy = 2;
        panel.add(new JLabel("Slot Number:"), gbc);

        gbc.gridx = 1;
        gbc.gridy = 2;
        slotNumberLabel = new JLabel();
        panel.add(slotNumberLabel, gbc);

        // Duration
        gbc.gridx = 0;
        gbc.gridy = 3;
        panel.add(new JLabel("Duration:"), gbc);

        gbc.gridx = 1;
        gbc.gridy = 3;
        durationLabel = new JLabel();
        panel.add(durationLabel, gbc);

        // Fee
        gbc.gridx = 0;
        gbc.gridy = 4;
        panel.add(new JLabel("Fee:"), gbc);

        gbc.gridx = 1;
        gbc.gridy = 4;
        feeLabel = new JLabel();
        panel.add(feeLabel, gbc);

        // Exit Button
        gbc.gridx = 1;
        gbc.gridy = 5;
        gbc.anchor = GridBagConstraints.EAST;
        exitButton = new JButton("Process Exit");

        // Add a more explicit action listener
        ActionListener exitActionListener = new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                System.out.println("Exit button action listener triggered");
                processExit();
            }
        };
        exitButton.addActionListener(exitActionListener);

        panel.add(exitButton, gbc);

        return panel;
    }

    /**
     * Find a vehicle by its number
     */
    private void findVehicle() {
        String vehicleNumber = vehicleNumberField.getText().trim();

        // Validate input
        if (vehicleNumber.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Please enter a vehicle number", "Input Error", JOptionPane.ERROR_MESSAGE);
            return;
        }

        // Find the vehicle
        currentVehicle = parkingLotManager.findVehicle(vehicleNumber);

        if (currentVehicle != null) {
            // Display vehicle details
            displayVehicleDetails();

            // Enable exit button
            exitButton.setEnabled(true);
        } else {
            // Clear details and show error message
            clearVehicleDetails();
            JOptionPane.showMessageDialog(this, "Vehicle not found", "Search Error", JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * Display the details of the found vehicle
     */
    private void displayVehicleDetails() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        vehicleTypeLabel.setText(currentVehicle.getVehicleType());
        entryTimeLabel.setText(currentVehicle.getEntryTime().format(formatter));
        slotNumberLabel.setText(String.valueOf(currentVehicle.getSlotNumber()));
        durationLabel.setText(FeeCalculator.formatDuration(currentVehicle));
        feeLabel.setText(String.format("$%.2f", FeeCalculator.calculateFee(currentVehicle)));
    }

    /**
     * Clear the vehicle details display
     */
    private void clearVehicleDetails() {
        vehicleTypeLabel.setText("-");
        entryTimeLabel.setText("-");
        slotNumberLabel.setText("-");
        durationLabel.setText("-");
        feeLabel.setText("-");

        currentVehicle = null;
        exitButton.setEnabled(false);
    }

    /**
     * Process the vehicle exit
     */
    private void processExit() {
        // Debug message to confirm button click is working
        System.out.println("Process Exit button clicked");

        if (currentVehicle == null) {
            System.out.println("Error: Current vehicle is null");
            JOptionPane.showMessageDialog(this,
                    "Error: No vehicle selected",
                    "Exit Error", JOptionPane.ERROR_MESSAGE);
            return;
        }

        String vehicleNumber = currentVehicle.getVehicleNumber();
        System.out.println("Using alternative exit method for vehicle: " + vehicleNumber);

        // Get the sensor simulator from the main frame
        com.parkingsystem.controller.SensorSimulator sensorSimulator =
                ((MainFrame)SwingUtilities.getWindowAncestor(this)).getSensorSimulator();

        // Calculate fee before removing the vehicle
        double fee = FeeCalculator.calculateFee(currentVehicle);

        // Use the manual exit method
        boolean success = sensorSimulator.manuallyProcessExit(vehicleNumber);

        if (success) {
            // Show success message
            JOptionPane.showMessageDialog(this,
                    "Vehicle exit processed successfully!\nFee: $" + String.format("%.2f", fee),
                    "Exit Success", JOptionPane.INFORMATION_MESSAGE);

            // Clear the form and details
            vehicleNumberField.setText("");
            clearVehicleDetails();
        } else {
            System.out.println("Error: Failed to remove vehicle using alternative method");
            JOptionPane.showMessageDialog(this,
                    "Error: Failed to process vehicle exit",
                    "Exit Error", JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * Refresh the panel to update displayed information
     */
    public void refreshPanel() {
        // If a vehicle is currently displayed, refresh its details
        if (currentVehicle != null) {
            // Check if the vehicle is still in the parking lot
            Vehicle vehicle = parkingLotManager.findVehicle(currentVehicle.getVehicleNumber());
            if (vehicle != null) {
                currentVehicle = vehicle;
                displayVehicleDetails();
            } else {
                clearVehicleDetails();
            }
        }
    }
}
