package com.parkingsystem.controller;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.parkingsystem.model.Constants;
import com.parkingsystem.model.Vehicle;

/**
 * Unit tests for the ParkingLotManager class
 */
public class ParkingLotManagerTest {
    
    private ParkingLotManager parkingLotManager;
    
    @BeforeEach
    public void setUp() {
        // Create a parking lot with 5 two-wheeler slots and 3 four-wheeler slots
        parkingLotManager = new ParkingLotManager(5, 3);
    }
    
    @Test
    public void testParkVehicle() {
        // Create a two-wheeler vehicle
        Vehicle twoWheeler = new Vehicle("TW123", Constants.TWO_WHEELER);
        
        // Park the vehicle
        int slotNumber = parkingLotManager.parkVehicle(twoWheeler);
        
        // Assert that a slot was assigned
        assertTrue(slotNumber > 0);
        
        // Assert that the vehicle is in the parked vehicles map
        assertNotNull(parkingLotManager.findVehicle("TW123"));
        
        // Assert that the available slots count decreased
        assertEquals(4, parkingLotManager.getAvailableTwoWheelerSlots());
    }
    
    @Test
    public void testParkVehicleWhenFull() {
        // Fill all two-wheeler slots
        for (int i = 1; i <= 5; i++) {
            Vehicle vehicle = new Vehicle("TW" + i, Constants.TWO_WHEELER);
            parkingLotManager.parkVehicle(vehicle);
        }
        
        // Try to park another two-wheeler
        Vehicle extraVehicle = new Vehicle("TW6", Constants.TWO_WHEELER);
        int slotNumber = parkingLotManager.parkVehicle(extraVehicle);
        
        // Assert that no slot was assigned
        assertEquals(-1, slotNumber);
        
        // Assert that the vehicle is not in the parked vehicles map
        assertNull(parkingLotManager.findVehicle("TW6"));
    }
    
    @Test
    public void testRemoveVehicle() {
        // Park a vehicle
        Vehicle vehicle = new Vehicle("FW123", Constants.FOUR_WHEELER);
        parkingLotManager.parkVehicle(vehicle);
        
        // Remove the vehicle
        Vehicle removedVehicle = parkingLotManager.removeVehicle("FW123");
        
        // Assert that the vehicle was removed
        assertNotNull(removedVehicle);
        assertEquals("FW123", removedVehicle.getVehicleNumber());
        
        // Assert that the vehicle is no longer in the parked vehicles map
        assertNull(parkingLotManager.findVehicle("FW123"));
        
        // Assert that the available slots count increased
        assertEquals(3, parkingLotManager.getAvailableFourWheelerSlots());
    }
    
    @Test
    public void testRemoveNonExistentVehicle() {
        // Try to remove a vehicle that doesn't exist
        Vehicle removedVehicle = parkingLotManager.removeVehicle("NONEXISTENT");
        
        // Assert that no vehicle was removed
        assertNull(removedVehicle);
    }
    
    @Test
    public void testFindVehicle() {
        // Park a vehicle
        Vehicle vehicle = new Vehicle("TW456", Constants.TWO_WHEELER);
        parkingLotManager.parkVehicle(vehicle);
        
        // Find the vehicle
        Vehicle foundVehicle = parkingLotManager.findVehicle("TW456");
        
        // Assert that the vehicle was found
        assertNotNull(foundVehicle);
        assertEquals("TW456", foundVehicle.getVehicleNumber());
    }
    
    @Test
    public void testGetAvailableSlots() {
        // Initially, all slots should be available
        assertEquals(5, parkingLotManager.getAvailableTwoWheelerSlots());
        assertEquals(3, parkingLotManager.getAvailableFourWheelerSlots());
        
        // Park some vehicles
        parkingLotManager.parkVehicle(new Vehicle("TW1", Constants.TWO_WHEELER));
        parkingLotManager.parkVehicle(new Vehicle("TW2", Constants.TWO_WHEELER));
        parkingLotManager.parkVehicle(new Vehicle("FW1", Constants.FOUR_WHEELER));
        
        // Check available slots
        assertEquals(3, parkingLotManager.getAvailableTwoWheelerSlots());
        assertEquals(2, parkingLotManager.getAvailableFourWheelerSlots());
    }
}
