package com.parkingsystem.controller;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.LocalTime;

import com.parkingsystem.model.Constants;
import com.parkingsystem.model.Vehicle;

/**
 * Controller for handling dynamic pricing based on peak hours and weekends
 */
public class DynamicPricingController {
    
    /**
     * Calculate the total fee with dynamic pricing
     *
     * @param vehicle The vehicle for which to calculate the fee
     * @return The calculated fee with surcharges
     */
    public static double calculateDynamicFee(Vehicle vehicle) {
        double baseFee = getBaseFee(vehicle);
        double surcharge = calculateSurcharge(vehicle);
        
        // Store pricing details in vehicle for reporting
        vehicle.setBaseFee(baseFee);
        vehicle.setSurcharge(surcharge);
        vehicle.setIsPeakHour(isPeakHour(vehicle.getEntryTime()));
        
        return baseFee + surcharge;
    }
    
    /**
     * Get the base fee without surcharges
     *
     * @param vehicle The vehicle
     * @return The base fee
     */
    private static double getBaseFee(Vehicle vehicle) {
        if (vehicle.getSelectedTimeSlot() != null && !vehicle.getSelectedTimeSlot().isEmpty()) {
            // Time slot based pricing
            if (vehicle.getVehicleType().equals(Constants.TWO_WHEELER)) {
                return Constants.TWO_WHEELER_SLOT_RATE;
            } else {
                return Constants.FOUR_WHEELER_SLOT_RATE;
            }
        } else {
            // Legacy hourly pricing
            return FeeCalculator.calculateFee(vehicle);
        }
    }
    
    /**
     * Calculate surcharge based on peak hours and weekends
     *
     * @param vehicle The vehicle
     * @return The surcharge amount
     */
    private static double calculateSurcharge(Vehicle vehicle) {
        double baseFee = getBaseFee(vehicle);
        double totalSurcharge = 0.0;
        
        LocalDateTime entryTime = vehicle.getEntryTime();
        
        // Peak hour surcharge
        if (isPeakHour(entryTime)) {
            totalSurcharge += baseFee * Constants.PEAK_HOUR_SURCHARGE;
        }
        
        // Weekend surcharge
        if (isWeekend(entryTime)) {
            totalSurcharge += baseFee * (Constants.WEEKEND_MULTIPLIER - 1.0);
        }
        
        return totalSurcharge;
    }
    
    /**
     * Check if the given time is during peak hours
     *
     * @param dateTime The date and time to check
     * @return True if it's peak hour, false otherwise
     */
    public static boolean isPeakHour(LocalDateTime dateTime) {
        LocalTime time = dateTime.toLocalTime();
        
        // Morning rush: 7:00 AM - 9:00 AM
        LocalTime morningStart = LocalTime.parse(Constants.MORNING_RUSH_HOURS[0]);
        LocalTime morningEnd = LocalTime.parse(Constants.MORNING_RUSH_HOURS[1]);
        
        // Evening rush: 5:00 PM - 7:00 PM
        LocalTime eveningStart = LocalTime.parse(Constants.EVENING_RUSH_HOURS[0]);
        LocalTime eveningEnd = LocalTime.parse(Constants.EVENING_RUSH_HOURS[1]);
        
        return (time.isAfter(morningStart) && time.isBefore(morningEnd)) ||
               (time.isAfter(eveningStart) && time.isBefore(eveningEnd));
    }
    
    /**
     * Check if the given date is a weekend
     *
     * @param dateTime The date and time to check
     * @return True if it's weekend, false otherwise
     */
    public static boolean isWeekend(LocalDateTime dateTime) {
        DayOfWeek dayOfWeek = dateTime.getDayOfWeek();
        return dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY;
    }
    
    /**
     * Get pricing information for display
     *
     * @param vehicle The vehicle
     * @return Formatted pricing information string
     */
    public static String getPricingInfo(Vehicle vehicle) {
        double baseFee = getBaseFee(vehicle);
        double surcharge = calculateSurcharge(vehicle);
        boolean isPeak = isPeakHour(vehicle.getEntryTime());
        boolean isWeekend = isWeekend(vehicle.getEntryTime());
        
        StringBuilder info = new StringBuilder();
        info.append(String.format("Base Fee: ₹%.2f\n", baseFee));
        
        if (isPeak) {
            double peakSurcharge = baseFee * Constants.PEAK_HOUR_SURCHARGE;
            info.append(String.format("Peak Hour Surcharge (%.0f%%): ₹%.2f\n", 
                Constants.PEAK_HOUR_SURCHARGE * 100, peakSurcharge));
        }
        
        if (isWeekend) {
            double weekendSurcharge = baseFee * (Constants.WEEKEND_MULTIPLIER - 1.0);
            info.append(String.format("Weekend Surcharge (%.0f%%): ₹%.2f\n", 
                (Constants.WEEKEND_MULTIPLIER - 1.0) * 100, weekendSurcharge));
        }
        
        if (surcharge > 0) {
            info.append(String.format("Total Surcharge: ₹%.2f\n", surcharge));
        }
        
        info.append(String.format("Total Fee: ₹%.2f", baseFee + surcharge));
        
        return info.toString();
    }
    
    /**
     * Get current pricing status for display
     *
     * @return Current pricing status string
     */
    public static String getCurrentPricingStatus() {
        LocalDateTime now = LocalDateTime.now();
        StringBuilder status = new StringBuilder();
        
        status.append("Current Pricing Status:\n");
        
        if (isPeakHour(now)) {
            status.append("🔴 PEAK HOURS - ").append(Constants.PEAK_HOUR_SURCHARGE * 100).append("% surcharge\n");
        } else {
            status.append("🟢 Normal Hours\n");
        }
        
        if (isWeekend(now)) {
            status.append("📅 Weekend - ").append((Constants.WEEKEND_MULTIPLIER - 1.0) * 100).append("% surcharge\n");
        } else {
            status.append("📅 Weekday\n");
        }
        
        return status.toString();
    }
}
