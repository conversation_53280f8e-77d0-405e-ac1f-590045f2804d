package com.parkingsystem.controller;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.parkingsystem.model.Constants;
import com.parkingsystem.model.ParkingSlot;
import com.parkingsystem.model.Vehicle;

/**
 * Manages the parking lot operations
 */
public class ParkingLotManager implements Serializable {
    private static final long serialVersionUID = 1L;
    private List<ParkingSlot> twoWheelerSlots;
    private List<ParkingSlot> fourWheelerSlots;
    private Map<String, Vehicle> parkedVehicles;

    /**
     * Constructor to initialize the parking lot with default number of slots
     */
    public ParkingLotManager() {
        this(Constants.DEFAULT_TWO_WHEELER_SLOTS, Constants.DEFAULT_FOUR_WHEELER_SLOTS);
    }

    /**
     * Constructor to initialize the parking lot with specified number of slots
     *
     * @param twoWheelerSlotCount Number of two-wheeler slots
     * @param fourWheelerSlotCount Number of four-wheeler slots
     */
    public ParkingLotManager(int twoWheelerSlotCount, int fourWheelerSlotCount) {
        twoWheelerSlots = new ArrayList<>();
        fourWheelerSlots = new ArrayList<>();
        parkedVehicles = new HashMap<>();

        // Initialize two-wheeler slots
        for (int i = 1; i <= twoWheelerSlotCount; i++) {
            twoWheelerSlots.add(new ParkingSlot(i, Constants.TWO_WHEELER));
        }

        // Initialize four-wheeler slots
        for (int i = 1; i <= fourWheelerSlotCount; i++) {
            fourWheelerSlots.add(new ParkingSlot(i + twoWheelerSlotCount, Constants.FOUR_WHEELER));
        }
    }

    /**
     * Park a vehicle in the parking lot
     *
     * @param vehicle The vehicle to park
     * @return The assigned slot number, or -1 if no slot is available
     */
    public int parkVehicle(Vehicle vehicle) {
        // Check if vehicle is already parked
        if (parkedVehicles.containsKey(vehicle.getVehicleNumber())) {
            return -1;
        }

        List<ParkingSlot> slots;
        if (vehicle.getVehicleType().equals(Constants.TWO_WHEELER)) {
            slots = twoWheelerSlots;
        } else {
            slots = fourWheelerSlots;
        }

        // Find an empty slot
        for (ParkingSlot slot : slots) {
            if (!slot.isOccupied()) {
                slot.parkVehicle(vehicle);
                parkedVehicles.put(vehicle.getVehicleNumber(), vehicle);
                return slot.getSlotNumber();
            }
        }

        // No empty slot found
        return -1;
    }

    /**
     * Remove a vehicle from the parking lot
     *
     * @param vehicleNumber The vehicle number to remove
     * @return The removed vehicle, or null if the vehicle is not found
     */
    public Vehicle removeVehicle(String vehicleNumber) {
        Vehicle vehicle = parkedVehicles.get(vehicleNumber);
        if (vehicle == null) {
            return null;
        }

        // Find the slot where the vehicle is parked
        List<ParkingSlot> slots;
        if (vehicle.getVehicleType().equals(Constants.TWO_WHEELER)) {
            slots = twoWheelerSlots;
        } else {
            slots = fourWheelerSlots;
        }

        for (ParkingSlot slot : slots) {
            if (slot.isOccupied() && slot.getParkedVehicle().getVehicleNumber().equals(vehicleNumber)) {
                vehicle.setExitTime(LocalDateTime.now());
                slot.removeVehicle();
                parkedVehicles.remove(vehicleNumber);
                return vehicle;
            }
        }

        return null;
    }

    /**
     * Find a vehicle by its number
     *
     * @param vehicleNumber The vehicle number to find
     * @return The vehicle, or null if not found
     */
    public Vehicle findVehicle(String vehicleNumber) {
        return parkedVehicles.get(vehicleNumber);
    }

    /**
     * Get all two-wheeler slots
     *
     * @return List of two-wheeler slots
     */
    public List<ParkingSlot> getTwoWheelerSlots() {
        return twoWheelerSlots;
    }

    /**
     * Get all four-wheeler slots
     *
     * @return List of four-wheeler slots
     */
    public List<ParkingSlot> getFourWheelerSlots() {
        return fourWheelerSlots;
    }

    /**
     * Get the count of available two-wheeler slots
     *
     * @return Count of available slots
     */
    public int getAvailableTwoWheelerSlots() {
        int count = 0;
        for (ParkingSlot slot : twoWheelerSlots) {
            if (!slot.isOccupied()) {
                count++;
            }
        }
        return count;
    }

    /**
     * Get the count of available four-wheeler slots
     *
     * @return Count of available slots
     */
    public int getAvailableFourWheelerSlots() {
        int count = 0;
        for (ParkingSlot slot : fourWheelerSlots) {
            if (!slot.isOccupied()) {
                count++;
            }
        }
        return count;
    }

    /**
     * Get all parked vehicles
     *
     * @return Map of vehicle numbers to vehicles
     */
    public Map<String, Vehicle> getParkedVehicles() {
        return parkedVehicles;
    }
}
