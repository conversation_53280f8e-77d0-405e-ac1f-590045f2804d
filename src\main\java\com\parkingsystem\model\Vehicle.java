package com.parkingsystem.model;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Represents a vehicle in the parking system
 */
public class Vehicle implements Serializable {
    private static final long serialVersionUID = 1L;
    private String vehicleNumber;
    private String vehicleType;
    private LocalDateTime entryTime;
    private LocalDateTime exitTime;
    private int slotNumber;
    private String selectedTimeSlot; // Time slot selected by user (e.g., "06:00-10:00")
    private String customerPhone; // Customer phone number for SMS notifications
    private String paymentMethod; // Payment method used
    private double baseFee; // Base fee before surcharges
    private double surcharge; // Peak hour or weekend surcharge
    private boolean isPeakHour; // Whether parked during peak hours

    /**
     * Constructor for creating a new vehicle entry
     *
     * @param vehicleNumber The license plate number
     * @param vehicleType The type of vehicle (Two Wheeler/Four Wheeler)
     */
    public Vehicle(String vehicleNumber, String vehicleType) {
        this.vehicleNumber = vehicleNumber;
        this.vehicleType = vehicleType;
        this.entryTime = LocalDateTime.now();
    }

    /**
     * Constructor for creating a new vehicle entry with time slot
     *
     * @param vehicleNumber The license plate number
     * @param vehicleType The type of vehicle (Two Wheeler/Four Wheeler)
     * @param selectedTimeSlot The selected time slot
     */
    public Vehicle(String vehicleNumber, String vehicleType, String selectedTimeSlot) {
        this.vehicleNumber = vehicleNumber;
        this.vehicleType = vehicleType;
        this.entryTime = LocalDateTime.now();
        this.selectedTimeSlot = selectedTimeSlot;
    }

    /**
     * Get the vehicle's license plate number
     *
     * @return The vehicle number
     */
    public String getVehicleNumber() {
        return vehicleNumber;
    }

    /**
     * Get the vehicle type
     *
     * @return The vehicle type (Two Wheeler/Four Wheeler)
     */
    public String getVehicleType() {
        return vehicleType;
    }

    /**
     * Get the entry time of the vehicle
     *
     * @return The entry time
     */
    public LocalDateTime getEntryTime() {
        return entryTime;
    }

    /**
     * Set the exit time when the vehicle leaves
     *
     * @param exitTime The exit time
     */
    public void setExitTime(LocalDateTime exitTime) {
        this.exitTime = exitTime;
    }

    /**
     * Get the exit time of the vehicle
     *
     * @return The exit time
     */
    public LocalDateTime getExitTime() {
        return exitTime;
    }

    /**
     * Set the slot number assigned to this vehicle
     *
     * @param slotNumber The slot number
     */
    public void setSlotNumber(int slotNumber) {
        this.slotNumber = slotNumber;
    }

    /**
     * Get the slot number assigned to this vehicle
     *
     * @return The slot number
     */
    public int getSlotNumber() {
        return slotNumber;
    }

    /**
     * Set the selected time slot for this vehicle
     *
     * @param selectedTimeSlot The selected time slot
     */
    public void setSelectedTimeSlot(String selectedTimeSlot) {
        this.selectedTimeSlot = selectedTimeSlot;
    }

    /**
     * Get the selected time slot for this vehicle
     *
     * @return The selected time slot
     */
    public String getSelectedTimeSlot() {
        return selectedTimeSlot;
    }

    /**
     * Set the customer phone number
     *
     * @param customerPhone The customer phone number
     */
    public void setCustomerPhone(String customerPhone) {
        this.customerPhone = customerPhone;
    }

    /**
     * Get the customer phone number
     *
     * @return The customer phone number
     */
    public String getCustomerPhone() {
        return customerPhone;
    }

    /**
     * Set the payment method
     *
     * @param paymentMethod The payment method
     */
    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    /**
     * Get the payment method
     *
     * @return The payment method
     */
    public String getPaymentMethod() {
        return paymentMethod;
    }

    /**
     * Set the base fee
     *
     * @param baseFee The base fee
     */
    public void setBaseFee(double baseFee) {
        this.baseFee = baseFee;
    }

    /**
     * Get the base fee
     *
     * @return The base fee
     */
    public double getBaseFee() {
        return baseFee;
    }

    /**
     * Set the surcharge
     *
     * @param surcharge The surcharge amount
     */
    public void setSurcharge(double surcharge) {
        this.surcharge = surcharge;
    }

    /**
     * Get the surcharge
     *
     * @return The surcharge amount
     */
    public double getSurcharge() {
        return surcharge;
    }

    /**
     * Set whether this is a peak hour parking
     *
     * @param isPeakHour True if peak hour parking
     */
    public void setIsPeakHour(boolean isPeakHour) {
        this.isPeakHour = isPeakHour;
    }

    /**
     * Check if this is a peak hour parking
     *
     * @return True if peak hour parking
     */
    public boolean getIsPeakHour() {
        return isPeakHour;
    }

    @Override
    public String toString() {
        return "Vehicle [vehicleNumber=" + vehicleNumber + ", vehicleType=" + vehicleType + ", entryTime=" + entryTime
                + ", slotNumber=" + slotNumber + "]";
    }
}
