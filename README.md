# Smart Parking System

A Java Swing application for managing a parking lot, with support for two-wheeler and four-wheeler vehicles.

## Features

- Vehicle entry and exit management
- Real-time parking slot status visualization
- Automatic parking fee calculation
- Transaction logging
- Persistent data storage

## Requirements

- Java 8 or higher
- Gradle (for building)

## Project Structure

```
parking_system/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   ├── com/
│   │   │   │   ├── parkingsystem/
│   │   │   │   │   ├── model/
│   │   │   │   │   │   ├── Vehicle.java
│   │   │   │   │   │   ├── ParkingSlot.java
│   │   │   │   │   │   └── Constants.java
│   │   │   │   │   ├── controller/
│   │   │   │   │   │   ├── ParkingLotManager.java
│   │   │   │   │   │   ├── FeeCalculator.java
│   │   │   │   │   │   └── FileManager.java
│   │   │   │   │   ├── view/
│   │   │   │   │   │   ├── MainFrame.java
│   │   │   │   │   │   ├── EntryPanel.java
│   │   │   │   │   │   ├── ExitPanel.java
│   │   │   │   │   │   └── StatusPanel.java
│   │   │   │   │   └── Main.java
├── build.gradle
└── README.md
```

## How to Build and Run

### Using Gradle

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/parking_system.git
   cd parking_system
   ```

2. Build the project:
   ```
   gradle build
   ```

3. Run the application:
   ```
   gradle run
   ```

### Using JAR file

1. Build the JAR file:
   ```
   gradle jar
   ```

2. Run the JAR file:
   ```
   java -jar build/libs/parking_system-1.0-SNAPSHOT.jar
   ```

## Usage

1. **Vehicle Entry**:
   - Go to the "Vehicle Entry" tab
   - Enter the vehicle number
   - Select the vehicle type (Two Wheeler/Four Wheeler)
   - Click "Park Vehicle"

2. **Vehicle Exit**:
   - Go to the "Vehicle Exit" tab
   - Enter the vehicle number
   - Click "Find"
   - Review the parking details and fee
   - Click "Process Exit"

3. **Parking Status**:
   - Go to the "Parking Status" tab
   - View the status of all parking slots
   - Check the list of currently parked vehicles
   - Review transaction logs

## Configuration

You can modify the following constants in `Constants.java`:

- Parking rates
- Number of parking slots
- Minimum fees

## License

This project is licensed under the MIT License - see the LICENSE file for details.
