package com.parkingsystem.view;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.time.format.DateTimeFormatter;
import java.util.Map;

import com.parkingsystem.controller.FileManager;
import com.parkingsystem.controller.ParkingLotManager;
import com.parkingsystem.model.Constants;
import com.parkingsystem.model.ParkingSlot;
import com.parkingsystem.model.Vehicle;

/**
 * Panel for displaying parking status
 */
public class StatusPanel extends JPanel {
    private ParkingLotManager parkingLotManager;

    private JPanel slotsPanel;
    private JTable vehiclesTable;
    private DefaultTableModel tableModel;
    private JTextArea logTextArea;

    /**
     * Constructor to initialize the status panel
     *
     * @param parkingLotManager The parking lot manager
     */
    public StatusPanel(ParkingLotManager parkingLotManager) {
        this.parkingLotManager = parkingLotManager;

        setLayout(new BorderLayout());

        // Create tabbed pane for different views
        JTabbedPane tabbedPane = new JTabbedPane();

        // Create slots view
        slotsPanel = createSlotsPanel();
        tabbedPane.addTab("Slots View", new JScrollPane(slotsPanel));

        // Create vehicles table
        JPanel vehiclesPanel = createVehiclesPanel();
        tabbedPane.addTab("Parked Vehicles", vehiclesPanel);

        // Create log view
        JPanel logPanel = createLogPanel();
        tabbedPane.addTab("Transaction Log", logPanel);

        add(tabbedPane, BorderLayout.CENTER);

        // Refresh the panel
        refreshPanel();
    }

    /**
     * Create the slots panel showing parking slots
     *
     * @return The slots panel
     */
    private JPanel createSlotsPanel() {
        JPanel panel = new JPanel(new BorderLayout());

        // Create a panel for two-wheeler slots
        JPanel twoWheelerPanel = new JPanel(new GridLayout(0, 5, 5, 5));
        twoWheelerPanel.setBorder(BorderFactory.createTitledBorder("Two Wheeler Slots"));

        // Create a panel for four-wheeler slots
        JPanel fourWheelerPanel = new JPanel(new GridLayout(0, 5, 5, 5));
        fourWheelerPanel.setBorder(BorderFactory.createTitledBorder("Four Wheeler Slots"));

        // Add the panels to the main panel
        panel.add(twoWheelerPanel, BorderLayout.NORTH);
        panel.add(fourWheelerPanel, BorderLayout.CENTER);

        return panel;
    }

    /**
     * Create the vehicles panel showing parked vehicles
     *
     * @return The vehicles panel
     */
    private JPanel createVehiclesPanel() {
        JPanel panel = new JPanel(new BorderLayout());

        // Create table model
        String[] columnNames = {"Vehicle Number", "Vehicle Type", "Entry Time", "Slot Number"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        // Create table
        vehiclesTable = new JTable(tableModel);
        vehiclesTable.getTableHeader().setReorderingAllowed(false);

        // Add table to panel
        panel.add(new JScrollPane(vehiclesTable), BorderLayout.CENTER);

        return panel;
    }

    /**
     * Create the log panel showing transaction logs
     *
     * @return The log panel
     */
    private JPanel createLogPanel() {
        JPanel panel = new JPanel(new BorderLayout());

        // Create text area for logs
        logTextArea = new JTextArea();
        logTextArea.setEditable(false);

        // Add text area to panel
        panel.add(new JScrollPane(logTextArea), BorderLayout.CENTER);

        // Add refresh button
        JButton refreshButton = new JButton("Refresh Logs");
        refreshButton.addActionListener(e -> refreshLogs());
        panel.add(refreshButton, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * Refresh the slots panel to show current status
     */
    private void refreshSlotsPanel() {
        // Clear the panel
        slotsPanel.removeAll();

        // Create a panel for two-wheeler slots
        JPanel twoWheelerPanel = new JPanel(new GridLayout(0, 5, 5, 5));
        twoWheelerPanel.setBorder(BorderFactory.createTitledBorder("Two Wheeler Slots"));

        // Create a panel for four-wheeler slots
        JPanel fourWheelerPanel = new JPanel(new GridLayout(0, 5, 5, 5));
        fourWheelerPanel.setBorder(BorderFactory.createTitledBorder("Four Wheeler Slots"));

        // Add two-wheeler slots
        for (ParkingSlot slot : parkingLotManager.getTwoWheelerSlots()) {
            JButton slotButton = createSlotButton(slot);
            twoWheelerPanel.add(slotButton);
        }

        // Add four-wheeler slots
        for (ParkingSlot slot : parkingLotManager.getFourWheelerSlots()) {
            JButton slotButton = createSlotButton(slot);
            fourWheelerPanel.add(slotButton);
        }

        // Add the panels to the main panel
        slotsPanel.setLayout(new BorderLayout());
        slotsPanel.add(twoWheelerPanel, BorderLayout.NORTH);
        slotsPanel.add(fourWheelerPanel, BorderLayout.CENTER);

        // Refresh the panel
        slotsPanel.revalidate();
        slotsPanel.repaint();
    }

    /**
     * Create a button representing a parking slot
     *
     * @param slot The parking slot
     * @return The button
     */
    private JButton createSlotButton(ParkingSlot slot) {
        JButton button = new JButton(String.valueOf(slot.getSlotNumber()));

        // Set button color based on slot status
        if (slot.isOccupied()) {
            button.setBackground(Color.RED);
            button.setForeground(Color.WHITE);

            // Add tooltip with vehicle info
            Vehicle vehicle = slot.getParkedVehicle();
            if (vehicle != null) {
                button.setToolTipText(vehicle.getVehicleNumber());
            }
        } else {
            button.setBackground(Color.GREEN);
            button.setForeground(Color.BLACK);
            button.setToolTipText("Available");
        }

        return button;
    }

    /**
     * Refresh the vehicles table to show current parked vehicles
     */
    private void refreshVehiclesTable() {
        // Clear the table
        tableModel.setRowCount(0);

        // Add parked vehicles to the table
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        Map<String, Vehicle> parkedVehicles = parkingLotManager.getParkedVehicles();

        for (Vehicle vehicle : parkedVehicles.values()) {
            Object[] row = {
                vehicle.getVehicleNumber(),
                vehicle.getVehicleType(),
                vehicle.getEntryTime().format(formatter),
                vehicle.getSlotNumber()
            };
            tableModel.addRow(row);
        }
    }

    /**
     * Refresh the logs to show current transaction logs
     */
    private void refreshLogs() {
        // Clear the text area
        logTextArea.setText("");

        // Add logs to the text area
        for (String log : FileManager.getTransactionLogs()) {
            logTextArea.append(log + "\n");
        }

        // Scroll to the bottom
        logTextArea.setCaretPosition(logTextArea.getDocument().getLength());
    }

    /**
     * Refresh the panel to update displayed information
     */
    public void refreshPanel() {
        refreshSlotsPanel();
        refreshVehiclesTable();
        refreshLogs();
    }
}
