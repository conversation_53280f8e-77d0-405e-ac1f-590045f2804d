package com.parkingsystem.model;

/**
 * Constants used throughout the parking system
 *
 * Includes configuration for parking rates, slot counts, and sensor parameters
 */
public class Constants {
    // Vehicle types
    public static final String TWO_WHEELER = "Two Wheeler";
    public static final String FOUR_WHEELER = "Four Wheeler";

    // Time slot definitions (24-hour format)
    public static final String[] TIME_SLOTS = {
        "06:00-10:00", "10:00-14:00", "14:00-18:00", "18:00-22:00"
    };

    // Time slot pricing (in rupees)
    public static final double TWO_WHEELER_SLOT_RATE = 50.0; // ₹50 per time slot
    public static final double FOUR_WHEELER_SLOT_RATE = 100.0; // ₹100 per time slot

    // Minimum fee (for partial slot usage)
    public static final double TWO_WHEELER_MIN_FEE = 30.0; // ₹30
    public static final double FOUR_WHEELER_MIN_FEE = 60.0; // ₹60

    // Parking lot configuration
    public static final int DEFAULT_TWO_WHEELER_SLOTS = 20;
    public static final int DEFAULT_FOUR_WHEELER_SLOTS = 10;

    // GUI constants
    public static final String APP_TITLE = "Smart Parking System";
    public static final int WINDOW_WIDTH = 800;
    public static final int WINDOW_HEIGHT = 600;

    // Panel titles
    public static final String ENTRY_PANEL_TITLE = "Vehicle Entry";
    public static final String EXIT_PANEL_TITLE = "Vehicle Exit";
    public static final String STATUS_PANEL_TITLE = "Parking Status";

    // Sensor configuration
    public static final int ENTRY_SENSOR_INTERVAL = 15; // seconds between entry sensor readings
    public static final int EXIT_SENSOR_INTERVAL = 20; // seconds between exit sensor readings

    // Time limits and fines
    public static final int PARKING_CLOSE_HOUR = 22; // Parking closes at 10 PM (22:00)
    public static final double OVERTIME_FINE = 500.0; // ₹500 fine for exceeding time slot

    // Dynamic Pricing - Peak Hours
    public static final String[] MORNING_RUSH_HOURS = {"07:00", "09:00"}; // 7 AM to 9 AM
    public static final String[] EVENING_RUSH_HOURS = {"17:00", "19:00"}; // 5 PM to 7 PM
    public static final double PEAK_HOUR_SURCHARGE = 0.5; // 50% surcharge during peak hours
    public static final double WEEKEND_MULTIPLIER = 1.2; // 20% extra on weekends

    // SMS Configuration
    public static final boolean SMS_ENABLED = true; // Enable/disable SMS notifications
    public static final String SMS_API_URL = "https://api.textlocal.in/send/"; // SMS API endpoint
    public static final String SMS_API_KEY = "YOUR_API_KEY_HERE"; // Replace with actual API key
    public static final String SMS_SENDER = "PARKING"; // SMS sender ID

    // Payment Methods
    public static final String[] PAYMENT_METHODS = {
        "Cash", "UPI", "Credit Card", "Debit Card", "Digital Wallet"
    };
    public static final String[] UPI_APPS = {
        "GPay", "PhonePe", "Paytm", "BHIM", "Other"
    };

    // Admin Dashboard
    public static final String ADMIN_USERNAME = "admin";
    public static final String ADMIN_PASSWORD = "parking123"; // Change this in production
    public static final String REPORTS_DIRECTORY = "data/reports";

    // Legacy constants for backward compatibility (converted to rupees)
    public static final int MAX_PARKING_HOURS = 8; // Maximum allowed parking hours
    public static final double OVERSTAY_FINE_PER_HOUR = 250.0; // ₹250 per hour for overstaying
    public static final double OVERSTAY_BASE_FINE = 500.0; // ₹500 base fine for any overstay
    public static final double TWO_WHEELER_RATE = 50.0; // ₹50 per hour (legacy)
    public static final double FOUR_WHEELER_RATE = 100.0; // ₹100 per hour (legacy)
}
