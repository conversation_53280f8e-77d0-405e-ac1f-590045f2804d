package com.parkingsystem.controller;

import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import javax.imageio.ImageIO;

import com.parkingsystem.model.Vehicle;

/**
 * Utility class for generating QR codes for vehicle tracking
 */
public class QRCodeGenerator {
    
    private static final String QR_CODE_DIRECTORY = "data/qrcodes";
    private static final String WEB_TEMPLATE_DIRECTORY = "data/web";
    private static final String BASE_URL = "http://smartparking.com/track?id=";
    
    /**
     * Initialize the QR code generator by creating necessary directories
     */
    public static void initialize() {
        // Create QR code directory if it doesn't exist
        File qrDir = new File(QR_CODE_DIRECTORY);
        if (!qrDir.exists()) {
            qrDir.mkdirs();
        }
        
        // Create web template directory if it doesn't exist
        File webDir = new File(WEB_TEMPLATE_DIRECTORY);
        if (!webDir.exists()) {
            webDir.mkdirs();
        }
        
        // Create the web template file if it doesn't exist
        createWebTemplate();
    }
    
    /**
     * Generate a QR code for a vehicle
     * 
     * @param vehicle The vehicle to generate a QR code for
     * @return The path to the generated QR code image
     */
    public static String generateQRCode(Vehicle vehicle) {
        initialize();
        
        // Create a unique tracking ID for the vehicle
        String trackingId = vehicle.getVehicleNumber().replace(" ", "") + "_" + System.currentTimeMillis();
        
        // Generate a simple QR code (simulated)
        String qrCodePath = QR_CODE_DIRECTORY + File.separator + trackingId + ".png";
        generateSimpleQRCode(BASE_URL + trackingId, qrCodePath);
        
        // Create a web page for this vehicle
        createVehicleWebPage(vehicle, trackingId);
        
        return qrCodePath;
    }
    
    /**
     * Generate a simple QR code image (simulated)
     * In a real implementation, this would use a QR code library
     * 
     * @param content The content to encode in the QR code
     * @param outputPath The path to save the QR code image
     */
    private static void generateSimpleQRCode(String content, String outputPath) {
        // This is a simplified simulation of QR code generation
        // In a real implementation, you would use a library like ZXing
        
        // Create a simple image with the URL text
        int width = 200;
        int height = 200;
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        // Fill background
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, width, height);
        
        // Draw border
        g2d.setColor(Color.BLACK);
        g2d.drawRect(10, 10, width - 20, height - 20);
        
        // Draw a pattern that looks like a QR code
        g2d.setColor(Color.BLACK);
        for (int i = 0; i < 10; i++) {
            for (int j = 0; j < 10; j++) {
                if (Math.random() > 0.5) {
                    g2d.fillRect(20 + i * 16, 20 + j * 16, 16, 16);
                }
            }
        }
        
        // Draw the corners (typical QR code markers)
        g2d.fillRect(20, 20, 48, 48);
        g2d.fillRect(width - 68, 20, 48, 48);
        g2d.fillRect(20, height - 68, 48, 48);
        
        g2d.setColor(Color.WHITE);
        g2d.fillRect(28, 28, 32, 32);
        g2d.fillRect(width - 60, 28, 32, 32);
        g2d.fillRect(28, height - 60, 32, 32);
        
        g2d.setColor(Color.BLACK);
        g2d.fillRect(36, 36, 16, 16);
        g2d.fillRect(width - 52, 36, 16, 16);
        g2d.fillRect(36, height - 52, 16, 16);
        
        // Add text at the bottom
        g2d.setColor(Color.BLACK);
        g2d.drawString("Scan to track your vehicle", 30, height - 10);
        
        g2d.dispose();
        
        try {
            File outputFile = new File(outputPath);
            ImageIO.write(image, "png", outputFile);
            System.out.println("QR Code generated at: " + outputPath);
        } catch (IOException e) {
            System.err.println("Error generating QR code: " + e.getMessage());
        }
    }
    
    /**
     * Create a web template file for vehicle tracking
     */
    private static void createWebTemplate() {
        String templatePath = WEB_TEMPLATE_DIRECTORY + File.separator + "template.html";
        File templateFile = new File(templatePath);
        
        if (!templateFile.exists()) {
            String htmlTemplate = 
                "<!DOCTYPE html>\n" +
                "<html>\n" +
                "<head>\n" +
                "    <title>Smart Parking - Vehicle Tracking</title>\n" +
                "    <style>\n" +
                "        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }\n" +
                "        .container { max-width: 800px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }\n" +
                "        h1 { color: #2c3e50; }\n" +
                "        .info-box { border: 1px solid #ddd; padding: 15px; margin-bottom: 20px; border-radius: 5px; }\n" +
                "        .label { font-weight: bold; width: 150px; display: inline-block; }\n" +
                "        .value { color: #2c3e50; }\n" +
                "        .status { padding: 10px; background-color: #e74c3c; color: white; border-radius: 5px; text-align: center; margin-top: 20px; }\n" +
                "        .status.parked { background-color: #27ae60; }\n" +
                "        .status.overstayed { background-color: #e74c3c; animation: blink 1s infinite; }\n" +
                "        @keyframes blink { 0% { opacity: 1; } 50% { opacity: 0.5; } 100% { opacity: 1; } }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <div class=\"container\">\n" +
                "        <h1>Smart Parking System</h1>\n" +
                "        <div class=\"info-box\">\n" +
                "            <p><span class=\"label\">Vehicle Number:</span> <span class=\"value\">{{VEHICLE_NUMBER}}</span></p>\n" +
                "            <p><span class=\"label\">Vehicle Type:</span> <span class=\"value\">{{VEHICLE_TYPE}}</span></p>\n" +
                "            <p><span class=\"label\">Entry Time:</span> <span class=\"value\">{{ENTRY_TIME}}</span></p>\n" +
                "            <p><span class=\"label\">Slot Number:</span> <span class=\"value\">{{SLOT_NUMBER}}</span></p>\n" +
                "            <p><span class=\"label\">Current Duration:</span> <span class=\"value\">{{DURATION}}</span></p>\n" +
                "            <p><span class=\"label\">Estimated Fee:</span> <span class=\"value\">{{FEE}}</span></p>\n" +
                "        </div>\n" +
                "        <div class=\"status {{STATUS_CLASS}}\">{{STATUS_TEXT}}</div>\n" +
                "    </div>\n" +
                "</body>\n" +
                "</html>";
            
            try {
                java.io.FileWriter writer = new java.io.FileWriter(templateFile);
                writer.write(htmlTemplate);
                writer.close();
                System.out.println("Web template created at: " + templatePath);
            } catch (IOException e) {
                System.err.println("Error creating web template: " + e.getMessage());
            }
        }
    }
    
    /**
     * Create a web page for a specific vehicle
     * 
     * @param vehicle The vehicle to create a web page for
     * @param trackingId The tracking ID for the vehicle
     */
    private static void createVehicleWebPage(Vehicle vehicle, String trackingId) {
        String templatePath = WEB_TEMPLATE_DIRECTORY + File.separator + "template.html";
        String outputPath = WEB_TEMPLATE_DIRECTORY + File.separator + trackingId + ".html";
        
        try {
            // Read the template
            File templateFile = new File(templatePath);
            String template = "";
            java.util.Scanner scanner = new java.util.Scanner(templateFile);
            while (scanner.hasNextLine()) {
                template += scanner.nextLine() + "\n";
            }
            scanner.close();
            
            // Replace placeholders with vehicle information
            String entryTime = vehicle.getEntryTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String duration = FeeCalculator.formatDuration(vehicle);
            double totalFee = FeeCalculator.calculateFee(vehicle);
            double overstayFine = FeeCalculator.calculateOverstayFine(vehicle);

            String feeText;
            String statusClass;
            String statusText;

            if (FeeCalculator.isOverstayed(vehicle)) {
                feeText = String.format("$%.2f (includes $%.2f overstay fine)", totalFee, overstayFine);
                statusClass = "overstayed";
                statusText = "OVERSTAYED - Exceeds " + com.parkingsystem.model.Constants.MAX_PARKING_HOURS + "h limit";
                duration += " (OVERSTAYED)";
            } else {
                feeText = String.format("$%.2f", totalFee);
                statusClass = "parked";
                statusText = "Currently Parked";
            }

            template = template.replace("{{VEHICLE_NUMBER}}", vehicle.getVehicleNumber());
            template = template.replace("{{VEHICLE_TYPE}}", vehicle.getVehicleType());
            template = template.replace("{{ENTRY_TIME}}", entryTime);
            template = template.replace("{{SLOT_NUMBER}}", String.valueOf(vehicle.getSlotNumber()));
            template = template.replace("{{DURATION}}", duration);
            template = template.replace("{{FEE}}", feeText);
            template = template.replace("{{STATUS_CLASS}}", statusClass);
            template = template.replace("{{STATUS_TEXT}}", statusText);
            
            // Write the output file
            File outputFile = new File(outputPath);
            java.io.FileWriter writer = new java.io.FileWriter(outputFile);
            writer.write(template);
            writer.close();
            
            System.out.println("Vehicle web page created at: " + outputPath);
        } catch (IOException e) {
            System.err.println("Error creating vehicle web page: " + e.getMessage());
        }
    }
}
