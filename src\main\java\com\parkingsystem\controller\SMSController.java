package com.parkingsystem.controller;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.CompletableFuture;

import com.parkingsystem.model.Constants;
import com.parkingsystem.model.Vehicle;

/**
 * Controller for handling SMS notifications
 */
public class SMSController {
    
    /**
     * Send entry confirmation SMS
     *
     * @param vehicle The vehicle that entered
     */
    public static void sendEntryConfirmation(Vehicle vehicle) {
        if (!Constants.SMS_ENABLED || vehicle.getCustomerPhone() == null || vehicle.getCustomerPhone().isEmpty()) {
            return;
        }
        
        String message = createEntryMessage(vehicle);
        sendSMSAsync(vehicle.getCustomerPhone(), message);
    }
    
    /**
     * Send exit confirmation SMS
     *
     * @param vehicle The vehicle that exited
     * @param totalFee The total fee charged
     */
    public static void sendExitConfirmation(Vehicle vehicle, double totalFee) {
        if (!Constants.SMS_ENABLED || vehicle.getCustomerPhone() == null || vehicle.getCustomerPhone().isEmpty()) {
            return;
        }
        
        String message = createExitMessage(vehicle, totalFee);
        sendSMSAsync(vehicle.getCustomerPhone(), message);
    }
    
    /**
     * Send overtime warning SMS
     *
     * @param vehicle The vehicle approaching overtime
     */
    public static void sendOvertimeWarning(Vehicle vehicle) {
        if (!Constants.SMS_ENABLED || vehicle.getCustomerPhone() == null || vehicle.getCustomerPhone().isEmpty()) {
            return;
        }
        
        String message = createOvertimeWarningMessage(vehicle);
        sendSMSAsync(vehicle.getCustomerPhone(), message);
    }
    
    /**
     * Create entry confirmation message
     *
     * @param vehicle The vehicle
     * @return The SMS message
     */
    private static String createEntryMessage(Vehicle vehicle) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm");
        
        StringBuilder message = new StringBuilder();
        message.append("🚗 PARKING ENTRY CONFIRMED\n");
        message.append("Vehicle: ").append(vehicle.getVehicleNumber()).append("\n");
        message.append("Slot: ").append(vehicle.getSlotNumber()).append("\n");
        message.append("Entry: ").append(vehicle.getEntryTime().format(formatter)).append("\n");
        
        if (vehicle.getSelectedTimeSlot() != null) {
            message.append("Time Slot: ").append(vehicle.getSelectedTimeSlot()).append("\n");
            
            // Add pricing info
            String pricingInfo = DynamicPricingController.getPricingInfo(vehicle);
            String[] lines = pricingInfo.split("\n");
            for (String line : lines) {
                if (line.contains("Total Fee:")) {
                    message.append(line).append("\n");
                    break;
                }
            }
        }
        
        message.append("Thank you for using Smart Parking!");
        
        return message.toString();
    }
    
    /**
     * Create exit confirmation message
     *
     * @param vehicle The vehicle
     * @param totalFee The total fee
     * @return The SMS message
     */
    private static String createExitMessage(Vehicle vehicle, double totalFee) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm");
        
        StringBuilder message = new StringBuilder();
        message.append("🚗 PARKING EXIT CONFIRMED\n");
        message.append("Vehicle: ").append(vehicle.getVehicleNumber()).append("\n");
        message.append("Exit: ").append(vehicle.getExitTime().format(formatter)).append("\n");
        message.append("Duration: ").append(FeeCalculator.formatDuration(vehicle)).append("\n");
        message.append("Total Fee: ₹").append(String.format("%.2f", totalFee)).append("\n");
        
        if (vehicle.getPaymentMethod() != null) {
            message.append("Payment: ").append(vehicle.getPaymentMethod()).append("\n");
        }
        
        // Check for overtime/overstay
        double overtimeFine = FeeCalculator.getOvertimeFine(vehicle);
        double overstayFine = FeeCalculator.calculateOverstayFine(vehicle);
        
        if (overtimeFine > 0) {
            message.append("⚠️ Overtime Fine: ₹").append(String.format("%.2f", overtimeFine)).append("\n");
        }
        
        if (overstayFine > 0) {
            message.append("⚠️ Overstay Fine: ₹").append(String.format("%.2f", overstayFine)).append("\n");
        }
        
        message.append("Thank you for using Smart Parking!");
        
        return message.toString();
    }
    
    /**
     * Create overtime warning message
     *
     * @param vehicle The vehicle
     * @return The SMS message
     */
    private static String createOvertimeWarningMessage(Vehicle vehicle) {
        StringBuilder message = new StringBuilder();
        message.append("⚠️ PARKING TIME SLOT EXPIRING\n");
        message.append("Vehicle: ").append(vehicle.getVehicleNumber()).append("\n");
        message.append("Slot: ").append(vehicle.getSlotNumber()).append("\n");
        
        if (vehicle.getSelectedTimeSlot() != null) {
            message.append("Time Slot: ").append(vehicle.getSelectedTimeSlot()).append("\n");
            message.append("Please exit within 30 minutes to avoid ₹").append(Constants.OVERTIME_FINE).append(" fine.\n");
        }
        
        message.append("Smart Parking System");
        
        return message.toString();
    }
    
    /**
     * Send SMS asynchronously
     *
     * @param phoneNumber The recipient phone number
     * @param message The message to send
     */
    private static void sendSMSAsync(String phoneNumber, String message) {
        CompletableFuture.runAsync(() -> {
            try {
                sendSMS(phoneNumber, message);
            } catch (Exception e) {
                System.err.println("Failed to send SMS: " + e.getMessage());
                // Log to file for debugging
                logSMSFailure(phoneNumber, message, e.getMessage());
            }
        });
    }
    
    /**
     * Send SMS using HTTP API (simulated)
     *
     * @param phoneNumber The recipient phone number
     * @param message The message to send
     * @throws Exception If SMS sending fails
     */
    private static void sendSMS(String phoneNumber, String message) throws Exception {
        // For demo purposes, we'll just log the SMS instead of actually sending it
        // In production, you would integrate with a real SMS API like TextLocal, Twilio, etc.
        
        System.out.println("=== SMS NOTIFICATION ===");
        System.out.println("To: " + phoneNumber);
        System.out.println("Message: " + message);
        System.out.println("========================");
        
        // Simulate API call delay
        Thread.sleep(1000);
        
        // Uncomment below for real SMS API integration:
        /*
        String encodedMessage = URLEncoder.encode(message, "UTF-8");
        String encodedSender = URLEncoder.encode(Constants.SMS_SENDER, "UTF-8");
        
        String postData = "apikey=" + Constants.SMS_API_KEY +
                         "&numbers=" + phoneNumber +
                         "&message=" + encodedMessage +
                         "&sender=" + encodedSender;
        
        URL url = new URL(Constants.SMS_API_URL);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setDoOutput(true);
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
        
        try (OutputStream os = connection.getOutputStream()) {
            os.write(postData.getBytes());
        }
        
        int responseCode = connection.getResponseCode();
        if (responseCode != 200) {
            throw new Exception("SMS API returned error code: " + responseCode);
        }
        */
    }
    
    /**
     * Log SMS sending failures
     *
     * @param phoneNumber The phone number
     * @param message The message
     * @param error The error message
     */
    private static void logSMSFailure(String phoneNumber, String message, String error) {
        System.err.println("SMS FAILURE LOG:");
        System.err.println("Phone: " + phoneNumber);
        System.err.println("Error: " + error);
        System.err.println("Message: " + message);
        System.err.println("---");
    }
    
    /**
     * Validate phone number format
     *
     * @param phoneNumber The phone number to validate
     * @return True if valid, false otherwise
     */
    public static boolean isValidPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            return false;
        }
        
        // Remove spaces and special characters
        String cleaned = phoneNumber.replaceAll("[\\s\\-\\(\\)\\+]", "");
        
        // Check if it's a valid Indian mobile number (10 digits starting with 6-9)
        return cleaned.matches("^[6-9]\\d{9}$") || cleaned.matches("^91[6-9]\\d{9}$");
    }
}
