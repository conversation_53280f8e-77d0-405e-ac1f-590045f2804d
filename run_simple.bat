@echo off
echo Compiling Smart Parking System...
mkdir -p build\classes
javac -d build\classes src\main\java\com\parkingsystem\model\*.java src\main\java\com\parkingsystem\controller\*.java src\main\java\com\parkingsystem\view\*.java src\main\java\com\parkingsystem\Main.java

if %ERRORLEVEL% NEQ 0 (
    echo Compilation failed!
    pause
    exit /b %ERRORLEVEL%
)

echo Running Smart Parking System...
java -cp build\classes com.parkingsystem.Main

pause
