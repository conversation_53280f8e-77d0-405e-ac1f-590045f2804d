package com.parkingsystem.controller;

import java.time.LocalDateTime;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import com.parkingsystem.model.Constants;
import com.parkingsystem.model.Vehicle;
import com.parkingsystem.view.MainFrame;

/**
 * Simulates parking sensors for automatic vehicle detection
 * This class provides a simulation of real-world sensors that would detect
 * vehicles entering and exiting the parking lot
 */
public class SensorSimulator {
    private ParkingLotManager parkingLotManager;
    private MainFrame mainFrame;
    private ScheduledExecutorService scheduler;
    private Random random;
    private boolean simulationActive = false;

    // License plate prefixes for simulation
    private static final String[] LICENSE_PREFIXES = {"ABC", "XYZ", "DEF", "GHI", "JKL"};

    /**
     * Constructor to initialize the sensor simulator
     *
     * @param parkingLotManager The parking lot manager
     * @param mainFrame The main application frame
     */
    public SensorSimulator(ParkingLotManager parkingLotManager, MainFrame mainFrame) {
        this.parkingLotManager = parkingLotManager;
        this.mainFrame = mainFrame;
        this.random = new Random();
    }

    /**
     * Start the sensor simulation
     */
    public void startSimulation() {
        if (simulationActive) {
            return;
        }

        simulationActive = true;
        scheduler = Executors.newScheduledThreadPool(2);

        // Schedule entry simulation
        scheduler.scheduleAtFixedRate(() -> {
            if (simulationActive) {
                simulateVehicleEntry();
            }
        }, 5, 15, TimeUnit.SECONDS);

        // Schedule exit simulation
        scheduler.scheduleAtFixedRate(() -> {
            if (simulationActive) {
                simulateVehicleExit();
            }
        }, 10, 20, TimeUnit.SECONDS);

        // Schedule overstay monitoring
        scheduler.scheduleAtFixedRate(() -> {
            if (simulationActive) {
                checkForOverstayingVehicles();
            }
        }, 30, 60, TimeUnit.SECONDS); // Check every minute after 30 seconds

        System.out.println("Sensor simulation started");
    }

    /**
     * Stop the sensor simulation
     */
    public void stopSimulation() {
        if (!simulationActive) {
            return;
        }

        simulationActive = false;
        if (scheduler != null) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
            }
        }

        System.out.println("Sensor simulation stopped");
    }

    /**
     * Simulate a vehicle entry event from entry gate sensors
     */
    private void simulateVehicleEntry() {
        // Check if there are available slots
        if (parkingLotManager.getAvailableTwoWheelerSlots() == 0 &&
            parkingLotManager.getAvailableFourWheelerSlots() == 0) {
            System.out.println("Sensor: No available slots for new vehicles");
            return;
        }

        // Generate a random vehicle
        String vehicleType = random.nextBoolean() ? Constants.TWO_WHEELER : Constants.FOUR_WHEELER;

        // If no slots available for this type, try the other type
        if ((vehicleType.equals(Constants.TWO_WHEELER) && parkingLotManager.getAvailableTwoWheelerSlots() == 0) ||
            (vehicleType.equals(Constants.FOUR_WHEELER) && parkingLotManager.getAvailableFourWheelerSlots() == 0)) {
            vehicleType = vehicleType.equals(Constants.TWO_WHEELER) ? Constants.FOUR_WHEELER : Constants.TWO_WHEELER;
        }

        // Generate a random license plate
        String prefix = LICENSE_PREFIXES[random.nextInt(LICENSE_PREFIXES.length)];
        String number = String.format("%03d", random.nextInt(1000));
        String vehicleNumber = prefix + number;

        // Create and park the vehicle
        Vehicle vehicle = new Vehicle(vehicleNumber, vehicleType);
        int slotNumber = parkingLotManager.parkVehicle(vehicle);

        if (slotNumber != -1) {
            // Log the entry
            FileManager.logEntry(vehicle);

            // Save parking data
            FileManager.saveParkingData(parkingLotManager);

            System.out.println("Sensor: Vehicle " + vehicleNumber + " (" + vehicleType + ") detected entering, assigned to slot " + slotNumber);

            // Refresh the UI
            mainFrame.refreshPanels();
        }
    }

    /**
     * Simulate a vehicle exit event from exit gate sensors
     */
    private void simulateVehicleExit() {
        // Check if there are any parked vehicles
        if (parkingLotManager.getParkedVehicles().isEmpty()) {
            System.out.println("Sensor: No vehicles to exit");
            return;
        }

        // Get a random vehicle from the parked vehicles
        Object[] vehicleNumbers = parkingLotManager.getParkedVehicles().keySet().toArray();
        String vehicleNumber = (String) vehicleNumbers[random.nextInt(vehicleNumbers.length)];
        Vehicle vehicle = parkingLotManager.findVehicle(vehicleNumber);

        if (vehicle != null) {
            // Set exit time
            vehicle.setExitTime(LocalDateTime.now());

            // Calculate fee
            double fee = FeeCalculator.calculateFee(vehicle);

            // Remove vehicle from parking lot
            Vehicle removedVehicle = parkingLotManager.removeVehicle(vehicleNumber);

            if (removedVehicle != null) {
                // Log the exit
                FileManager.logExit(removedVehicle, fee);

                // Save parking data
                FileManager.saveParkingData(parkingLotManager);

                System.out.println("Sensor: Vehicle " + vehicleNumber + " detected exiting, fee charged: $" + String.format("%.2f", fee));

                // Refresh the UI
                mainFrame.refreshPanels();
            }
        }
    }

    /**
     * Check if the simulation is active
     *
     * @return true if simulation is active, false otherwise
     */
    public boolean isSimulationActive() {
        return simulationActive;
    }

    /**
     * Manually process a vehicle exit (for use when the Process Exit button isn't working)
     *
     * @param vehicleNumber The vehicle number to process exit for
     * @return true if exit was processed successfully, false otherwise
     */
    public boolean manuallyProcessExit(String vehicleNumber) {
        Vehicle vehicle = parkingLotManager.findVehicle(vehicleNumber);

        if (vehicle != null) {
            // Set exit time
            vehicle.setExitTime(LocalDateTime.now());

            // Calculate fee
            double fee = FeeCalculator.calculateFee(vehicle);

            // Remove vehicle from parking lot
            Vehicle removedVehicle = parkingLotManager.removeVehicle(vehicleNumber);

            if (removedVehicle != null) {
                // Log the exit
                FileManager.logExit(removedVehicle, fee);

                // Save parking data
                FileManager.saveParkingData(parkingLotManager);

                System.out.println("Manual exit processed for vehicle " + vehicleNumber + ", fee charged: $" + String.format("%.2f", fee));

                // Refresh the UI
                mainFrame.refreshPanels();

                return true;
            }
        }

        return false;
    }

    /**
     * Check for vehicles that have overstayed the time limit
     */
    private void checkForOverstayingVehicles() {
        // Get all currently parked vehicles
        for (Vehicle vehicle : parkingLotManager.getParkedVehicles().values()) {
            if (FeeCalculator.isOverstayed(vehicle)) {
                // Calculate overstay fine
                double overstayFine = FeeCalculator.calculateOverstayFine(vehicle);

                System.out.println("OVERSTAY ALERT: Vehicle " + vehicle.getVehicleNumber() +
                                 " has exceeded the " + Constants.MAX_PARKING_HOURS +
                                 " hour limit. Additional fine: $" + String.format("%.2f", overstayFine));

                // Log the overstay violation
                FileManager.logOverstayViolation(vehicle, overstayFine);

                // In a real system, this could trigger:
                // - SMS/Email notifications to the vehicle owner
                // - Security alerts
                // - Automatic towing requests
                // - Mobile app push notifications
            }
        }
    }
}
