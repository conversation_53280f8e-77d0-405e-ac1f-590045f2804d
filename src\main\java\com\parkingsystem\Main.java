package com.parkingsystem;

import javax.swing.SwingUtilities;
import javax.swing.UIManager;

import com.parkingsystem.controller.FileManager;
import com.parkingsystem.controller.ParkingLotManager;
import com.parkingsystem.view.MainFrame;

/**
 * Main class for the Smart Parking System
 *
 * This system integrates with parking sensors for automatic vehicle detection:
 * - Entry sensors detect vehicles at the entry gate and assign parking slots
 * - Exit sensors detect vehicles at the exit gate and process payment
 * - All sensor data is processed in real-time for accurate parking management
 */
public class Main {
    /**
     * Entry point for the application
     *
     * @param args Command line arguments
     */
    public static void main(String[] args) {
        // Initialize file manager
        FileManager.initialize();

        // Try to load existing parking data
        ParkingLotManager parkingLotManager = FileManager.loadParkingData();

        // If no data exists, create a new parking lot manager
        if (parkingLotManager == null) {
            parkingLotManager = new ParkingLotManager();
        }

        // Create final reference for lambda
        final ParkingLotManager finalParkingLotManager = parkingLotManager;

        // Start the GUI
        SwingUtilities.invokeLater(() -> {
            try {
                // Set system look and feel
                UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
            } catch (Exception e) {
                e.printStackTrace();
            }

            // Create and show the main frame
            MainFrame mainFrame = new MainFrame(finalParkingLotManager);
            mainFrame.setVisible(true);
        });
    }
}
