package com.parkingsystem.view;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import com.parkingsystem.controller.DynamicPricingController;
import com.parkingsystem.controller.FeeCalculator;
import com.parkingsystem.controller.FileManager;
import com.parkingsystem.controller.ParkingLotManager;
import com.parkingsystem.controller.PaymentController;
import com.parkingsystem.controller.SMSController;
import com.parkingsystem.model.Constants;
import com.parkingsystem.model.Vehicle;

/**
 * A simplified panel for handling vehicle exits
 * This panel provides a more direct approach to processing vehicle exits
 */
public class QuickExitPanel extends JPanel {
    private ParkingLotManager parkingLotManager;
    private MainFrame mainFrame;
    
    private JTextField vehicleNumberField;
    private JButton findButton;
    private JButton exitButton;
    
    private JLabel vehicleTypeLabel;
    private JLabel entryTimeLabel;
    private JLabel slotNumberLabel;
    private JLabel timeSlotLabel;
    private JLabel durationLabel;
    private JLabel feeLabel;
    
    private Vehicle currentVehicle;
    
    /**
     * Constructor to initialize the quick exit panel
     * 
     * @param parkingLotManager The parking lot manager
     * @param mainFrame The main frame
     */
    public QuickExitPanel(ParkingLotManager parkingLotManager, MainFrame mainFrame) {
        this.parkingLotManager = parkingLotManager;
        this.mainFrame = mainFrame;
        
        setLayout(new BorderLayout());
        
        // Create search panel
        JPanel searchPanel = createSearchPanel();
        add(searchPanel, BorderLayout.NORTH);
        
        // Create details panel
        JPanel detailsPanel = createDetailsPanel();
        add(detailsPanel, BorderLayout.CENTER);
        
        // Initialize labels
        clearVehicleDetails();
        
        // Disable exit button initially
        exitButton.setEnabled(false);
    }
    
    /**
     * Create the search panel for finding vehicles
     * 
     * @return The search panel
     */
    private JPanel createSearchPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Find Vehicle"));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.fill = GridBagConstraints.HORIZONTAL;
        
        // Vehicle Number
        gbc.gridx = 0;
        gbc.gridy = 0;
        panel.add(new JLabel("Vehicle Number:"), gbc);
        
        gbc.gridx = 1;
        gbc.gridy = 0;
        vehicleNumberField = new JTextField(15);
        panel.add(vehicleNumberField, gbc);
        
        // Find Button
        gbc.gridx = 2;
        gbc.gridy = 0;
        findButton = new JButton("Find");
        findButton.addActionListener(e -> findVehicle());
        panel.add(findButton, gbc);
        
        return panel;
    }
    
    /**
     * Create the details panel for showing vehicle information
     * 
     * @return The details panel
     */
    private JPanel createDetailsPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Vehicle Details"));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.anchor = GridBagConstraints.WEST;
        
        // Vehicle Type
        gbc.gridx = 0;
        gbc.gridy = 0;
        panel.add(new JLabel("Vehicle Type:"), gbc);
        
        gbc.gridx = 1;
        gbc.gridy = 0;
        vehicleTypeLabel = new JLabel();
        panel.add(vehicleTypeLabel, gbc);
        
        // Entry Time
        gbc.gridx = 0;
        gbc.gridy = 1;
        panel.add(new JLabel("Entry Time:"), gbc);
        
        gbc.gridx = 1;
        gbc.gridy = 1;
        entryTimeLabel = new JLabel();
        panel.add(entryTimeLabel, gbc);
        
        // Slot Number
        gbc.gridx = 0;
        gbc.gridy = 2;
        panel.add(new JLabel("Slot Number:"), gbc);

        gbc.gridx = 1;
        gbc.gridy = 2;
        slotNumberLabel = new JLabel();
        panel.add(slotNumberLabel, gbc);

        // Time Slot
        gbc.gridx = 0;
        gbc.gridy = 3;
        panel.add(new JLabel("Time Slot:"), gbc);

        gbc.gridx = 1;
        gbc.gridy = 3;
        timeSlotLabel = new JLabel();
        panel.add(timeSlotLabel, gbc);

        // Duration
        gbc.gridx = 0;
        gbc.gridy = 4;
        panel.add(new JLabel("Duration:"), gbc);

        gbc.gridx = 1;
        gbc.gridy = 4;
        durationLabel = new JLabel();
        panel.add(durationLabel, gbc);

        // Fee
        gbc.gridx = 0;
        gbc.gridy = 5;
        panel.add(new JLabel("Fee:"), gbc);

        gbc.gridx = 1;
        gbc.gridy = 5;
        feeLabel = new JLabel();
        panel.add(feeLabel, gbc);

        // Exit Button
        gbc.gridx = 1;
        gbc.gridy = 6;
        gbc.anchor = GridBagConstraints.EAST;
        exitButton = new JButton("Quick Exit");
        exitButton.addActionListener(e -> quickExit());
        panel.add(exitButton, gbc);
        
        return panel;
    }
    
    /**
     * Find a vehicle by its number
     */
    private void findVehicle() {
        String vehicleNumber = vehicleNumberField.getText().trim();
        
        // Validate input
        if (vehicleNumber.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Please enter a vehicle number", "Input Error", JOptionPane.ERROR_MESSAGE);
            return;
        }
        
        // Find the vehicle
        currentVehicle = parkingLotManager.findVehicle(vehicleNumber);
        
        if (currentVehicle != null) {
            // Display vehicle details
            displayVehicleDetails();
            
            // Enable exit button
            exitButton.setEnabled(true);
        } else {
            // Clear details and show error message
            clearVehicleDetails();
            JOptionPane.showMessageDialog(this, "Vehicle not found", "Search Error", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * Display the details of the found vehicle
     */
    private void displayVehicleDetails() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        vehicleTypeLabel.setText(currentVehicle.getVehicleType());
        entryTimeLabel.setText(currentVehicle.getEntryTime().format(formatter));
        slotNumberLabel.setText(String.valueOf(currentVehicle.getSlotNumber()));

        // Display time slot information
        String timeSlot = currentVehicle.getSelectedTimeSlot();
        if (timeSlot != null && !timeSlot.isEmpty()) {
            timeSlotLabel.setText(timeSlot);
        } else {
            timeSlotLabel.setText("Legacy Entry (No Time Slot)");
        }

        // Format duration with overtime/overstay warning
        String duration = FeeCalculator.formatDuration(currentVehicle);
        boolean hasOvertime = false;
        boolean hasOverstay = false;

        // Check for overtime (time slot exceeded)
        double overtimeFine = FeeCalculator.getOvertimeFine(currentVehicle);
        if (overtimeFine > 0) {
            duration += " (OVERTIME - Exceeded time slot)";
            durationLabel.setForeground(Color.RED);
            hasOvertime = true;
        }

        // Check for overstay (legacy system)
        if (!hasOvertime && FeeCalculator.isOverstayed(currentVehicle)) {
            duration += " (OVERSTAYED - Exceeds " + Constants.MAX_PARKING_HOURS + "h limit)";
            durationLabel.setForeground(Color.RED);
            hasOverstay = true;
        }

        if (!hasOvertime && !hasOverstay) {
            durationLabel.setForeground(Color.BLACK);
        }
        durationLabel.setText(duration);

        // Calculate and display fee with dynamic pricing breakdown
        double totalFee = FeeCalculator.calculateFee(currentVehicle);
        double overstayFine = FeeCalculator.calculateOverstayFine(currentVehicle);

        // Get detailed pricing information
        String pricingInfo = DynamicPricingController.getPricingInfo(currentVehicle);
        String[] pricingLines = pricingInfo.split("\n");
        String totalFeeLine = pricingLines[pricingLines.length - 1]; // Last line has total fee

        if (hasOvertime) {
            feeLabel.setText(String.format("₹%.2f (includes ₹%.2f overtime fine)", totalFee, overtimeFine));
            feeLabel.setForeground(Color.RED);
        } else if (overstayFine > 0) {
            feeLabel.setText(String.format("₹%.2f (includes ₹%.2f overstay fine)", totalFee, overstayFine));
            feeLabel.setForeground(Color.RED);
        } else {
            // Show dynamic pricing details
            feeLabel.setText("<html>" + totalFeeLine);
            if (currentVehicle.getSurcharge() > 0) {
                feeLabel.setText(feeLabel.getText() + "<br><small>Includes surcharges</small></html>");
                feeLabel.setForeground(Color.BLUE);
            } else {
                feeLabel.setText(feeLabel.getText() + "</html>");
                feeLabel.setForeground(Color.BLACK);
            }
        }
    }
    
    /**
     * Clear the vehicle details display
     */
    private void clearVehicleDetails() {
        vehicleTypeLabel.setText("-");
        entryTimeLabel.setText("-");
        slotNumberLabel.setText("-");
        timeSlotLabel.setText("-");
        durationLabel.setText("-");
        feeLabel.setText("-");

        currentVehicle = null;
        exitButton.setEnabled(false);
    }
    
    /**
     * Process the vehicle exit using a direct approach
     */
    private void quickExit() {
        if (currentVehicle == null) {
            JOptionPane.showMessageDialog(this,
                    "No vehicle selected",
                    "Exit Error", JOptionPane.ERROR_MESSAGE);
            return;
        }

        try {
            System.out.println("Starting quick exit for vehicle: " + currentVehicle.getVehicleNumber());

            // Get vehicle info before removal
            String vehicleNumber = currentVehicle.getVehicleNumber();

            // Set exit time FIRST
            currentVehicle.setExitTime(LocalDateTime.now());
            System.out.println("Exit time set to: " + currentVehicle.getExitTime());

            // Calculate fee AFTER setting exit time
            double fee = FeeCalculator.calculateFee(currentVehicle);
            System.out.println("Fee calculated: ₹" + fee);

            // Calculate overtime and overstay fines for message
            double overtimeFine = FeeCalculator.getOvertimeFine(currentVehicle);
            double overstayFine = FeeCalculator.calculateOverstayFine(currentVehicle);
            System.out.println("Overtime fine: ₹" + overtimeFine);
            System.out.println("Overstay fine: ₹" + overstayFine);

            // Remove vehicle from parking lot using a more direct approach
            System.out.println("Attempting to remove vehicle from parking lot...");

            // Get the parked vehicles map and remove directly
            if (parkingLotManager.getParkedVehicles().containsKey(vehicleNumber)) {
                Vehicle vehicleToRemove = parkingLotManager.getParkedVehicles().get(vehicleNumber);

                // Free up the parking slot
                int slotNumber = vehicleToRemove.getSlotNumber();
                String vehicleType = vehicleToRemove.getVehicleType();

                if (vehicleType.equals(Constants.TWO_WHEELER)) {
                    parkingLotManager.getTwoWheelerSlots().get(slotNumber - 1).removeVehicle();
                } else {
                    parkingLotManager.getFourWheelerSlots().get(slotNumber - 1).removeVehicle();
                }

                // Remove from parked vehicles map
                parkingLotManager.getParkedVehicles().remove(vehicleNumber);

                System.out.println("Vehicle removed successfully from slot " + slotNumber);

                // Process payment
                String[] paymentOptions = Constants.PAYMENT_METHODS;
                String selectedPayment = (String) JOptionPane.showInputDialog(
                    this,
                    "Select Payment Method:\nTotal Amount: ₹" + String.format("%.2f", fee),
                    "Payment Selection",
                    JOptionPane.QUESTION_MESSAGE,
                    null,
                    paymentOptions,
                    paymentOptions[0]
                );

                if (selectedPayment == null) {
                    JOptionPane.showMessageDialog(this, "Payment cancelled. Vehicle exit aborted.", "Payment Cancelled", JOptionPane.WARNING_MESSAGE);
                    return;
                }

                PaymentController.PaymentResult paymentResult = PaymentController.processPayment(vehicleToRemove, fee, selectedPayment);

                if (!paymentResult.isSuccess()) {
                    JOptionPane.showMessageDialog(this, "Payment failed: " + paymentResult.getMessage(), "Payment Error", JOptionPane.ERROR_MESSAGE);
                    return;
                }

                // Log the exit
                FileManager.logExit(vehicleToRemove, fee);
                System.out.println("Exit logged");

                // Save parking data
                boolean saved = FileManager.saveParkingData(parkingLotManager);
                System.out.println("Parking data saved: " + saved);

                // Prepare success message with payment and fine information
                String message = "Vehicle exit processed successfully!\n";
                message += "Total Fee: ₹" + String.format("%.2f", fee) + "\n";
                message += "Payment Method: " + selectedPayment + "\n";

                if (paymentResult.getTransactionId() != null) {
                    message += "Transaction ID: " + paymentResult.getTransactionId() + "\n";
                }

                if (overtimeFine > 0) {
                    message += "\nOVERTIME PENALTY APPLIED:";
                    message += "\nTime slot exceeded: " + currentVehicle.getSelectedTimeSlot();
                    message += "\nOvertime fine: ₹" + String.format("%.2f", overtimeFine);
                    message += "\nBase parking fee: ₹" + String.format("%.2f", fee - overtimeFine);
                } else if (overstayFine > 0) {
                    message += "\nOVERSTAY PENALTY APPLIED:";
                    message += "\nTime limit exceeded: " + Constants.MAX_PARKING_HOURS + " hours";
                    message += "\nOverstay fine: ₹" + String.format("%.2f", overstayFine);
                    message += "\nRegular parking fee: ₹" + String.format("%.2f", fee - overstayFine);
                }

                // Send SMS notification
                SMSController.sendExitConfirmation(vehicleToRemove, fee);

                // Show success message
                JOptionPane.showMessageDialog(this, message, "Exit Success", JOptionPane.INFORMATION_MESSAGE);

                // Clear the form and details
                vehicleNumberField.setText("");
                clearVehicleDetails();

                // Refresh all panels
                mainFrame.refreshPanels();

            } else {
                System.out.println("Vehicle not found in parked vehicles map");
                JOptionPane.showMessageDialog(this,
                        "Vehicle not found in parking records",
                        "Exit Error", JOptionPane.ERROR_MESSAGE);
            }
        } catch (Exception ex) {
            // Handle any unexpected errors
            ex.printStackTrace();
            System.out.println("Exception during exit: " + ex.getMessage());
            JOptionPane.showMessageDialog(this,
                    "An error occurred: " + ex.getMessage(),
                    "System Error", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * Refresh the panel to update displayed information
     */
    public void refreshPanel() {
        // If a vehicle is currently displayed, refresh its details
        if (currentVehicle != null) {
            // Check if the vehicle is still in the parking lot
            Vehicle vehicle = parkingLotManager.findVehicle(currentVehicle.getVehicleNumber());
            if (vehicle != null) {
                currentVehicle = vehicle;
                displayVehicleDetails();
            } else {
                clearVehicleDetails();
            }
        }
    }
}
