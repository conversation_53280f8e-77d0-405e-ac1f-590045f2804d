package com.parkingsystem.view;

import javax.swing.*;
import java.awt.*;

import com.parkingsystem.controller.ParkingLotManager;
import com.parkingsystem.controller.SensorSimulator;
import com.parkingsystem.model.Constants;

/**
 * Main application window for the parking system
 */
public class MainFrame extends JFrame {
    private ParkingLotManager parkingLotManager;
    private EntryPanel entryPanel;
    private QuickExitPanel exitPanel; // Using QuickExitPanel instead of ExitPanel
    private StatusPanel statusPanel;
    private AdminDashboardPanel adminPanel;
    private SensorSimulator sensorSimulator;
    private JToggleButton simulationToggle;
    private JLabel sensorStatusLabel;

    /**
     * Constructor to initialize the main frame
     *
     * @param parkingLotManager The parking lot manager
     */
    public MainFrame(ParkingLotManager parkingLotManager) {
        this.parkingLotManager = parkingLotManager;

        // Initialize the sensor simulator
        this.sensorSimulator = new SensorSimulator(parkingLotManager, this);

        // Set up the frame
        setTitle(Constants.APP_TITLE);
        setSize(Constants.WINDOW_WIDTH, Constants.WINDOW_HEIGHT);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLocationRelativeTo(null);

        // Create the tabbed pane
        JTabbedPane tabbedPane = new JTabbedPane();

        // Create the panels
        entryPanel = new EntryPanel(parkingLotManager, this);
        exitPanel = new QuickExitPanel(parkingLotManager, this); // Using QuickExitPanel
        statusPanel = new StatusPanel(parkingLotManager);
        adminPanel = new AdminDashboardPanel(parkingLotManager, this);

        // Add panels to the tabbed pane
        tabbedPane.addTab(Constants.ENTRY_PANEL_TITLE, entryPanel);
        tabbedPane.addTab(Constants.EXIT_PANEL_TITLE, exitPanel);
        tabbedPane.addTab(Constants.STATUS_PANEL_TITLE, statusPanel);
        tabbedPane.addTab("Admin Dashboard", adminPanel);

        // Add the tabbed pane to the frame
        add(tabbedPane, BorderLayout.CENTER);

        // Create a status bar
        JPanel statusBar = createStatusBar();
        add(statusBar, BorderLayout.SOUTH);

        // Add window listener to stop simulation when closing
        addWindowListener(new java.awt.event.WindowAdapter() {
            @Override
            public void windowClosing(java.awt.event.WindowEvent windowEvent) {
                if (sensorSimulator.isSimulationActive()) {
                    sensorSimulator.stopSimulation();
                }
            }
        });
    }

    /**
     * Create a status bar for the application
     *
     * @return The status bar panel
     */
    private JPanel createStatusBar() {
        JPanel statusBar = new JPanel(new BorderLayout());
        statusBar.setBorder(BorderFactory.createEtchedBorder());

        // Left side - app name
        JLabel statusLabel = new JLabel(" Smart Parking System v1.0");
        statusBar.add(statusLabel, BorderLayout.WEST);

        // Center - sensor controls
        JPanel centerPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));

        // Sensor status label
        sensorStatusLabel = new JLabel("Sensors: Inactive");
        sensorStatusLabel.setForeground(Color.RED);
        centerPanel.add(sensorStatusLabel);

        // Simulation toggle button
        simulationToggle = new JToggleButton("Activate Sensors");
        simulationToggle.addActionListener(e -> {
            if (simulationToggle.isSelected()) {
                sensorSimulator.startSimulation();
                simulationToggle.setText("Deactivate Sensors");
                sensorStatusLabel.setText("Sensors: Active");
                sensorStatusLabel.setForeground(new Color(0, 128, 0)); // Dark green
            } else {
                sensorSimulator.stopSimulation();
                simulationToggle.setText("Activate Sensors");
                sensorStatusLabel.setText("Sensors: Inactive");
                sensorStatusLabel.setForeground(Color.RED);
            }
        });
        centerPanel.add(simulationToggle);

        statusBar.add(centerPanel, BorderLayout.CENTER);

        // Right side - time
        JLabel timeLabel = new JLabel();
        statusBar.add(timeLabel, BorderLayout.EAST);

        // Update time every second
        Timer timer = new Timer(1000, e -> {
            timeLabel.setText(java.time.LocalDateTime.now().format(
                    java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss ")) + " ");
        });
        timer.start();

        return statusBar;
    }

    /**
     * Refresh all panels to update their display
     */
    public void refreshPanels() {
        entryPanel.refreshPanel();
        exitPanel.refreshPanel();
        statusPanel.refreshPanel();
        adminPanel.refreshPanel();
    }

    /**
     * Refresh all panels except the specified one
     *
     * @param excludePanel The panel to exclude from refreshing
     */
    public void refreshOtherPanels(JPanel excludePanel) {
        if (excludePanel != entryPanel) {
            entryPanel.refreshPanel();
        }
        if (excludePanel != exitPanel) {
            exitPanel.refreshPanel();
        }
        if (excludePanel != statusPanel) {
            statusPanel.refreshPanel();
        }
        if (excludePanel != adminPanel) {
            adminPanel.refreshPanel();
        }
    }

    /**
     * Get the sensor simulator
     *
     * @return The sensor simulator
     */
    public SensorSimulator getSensorSimulator() {
        return sensorSimulator;
    }
}
