plugins {
    id 'java'
    id 'application'
}

group = 'com.parkingsystem'
version = '1.0-SNAPSHOT'

sourceCompatibility = 1.8
targetCompatibility = 1.8

repositories {
    mavenCentral()
}

dependencies {
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.8.1'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.8.1'
}

application {
    mainClassName = 'com.parkingsystem.Main'
}

jar {
    manifest {
        attributes 'Main-Class': 'com.parkingsystem.Main'
    }
    from {
        configurations.runtimeClasspath.collect { it.isDirectory() ? it : zipTree(it) }
    }
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

test {
    useJUnitPlatform()
}
