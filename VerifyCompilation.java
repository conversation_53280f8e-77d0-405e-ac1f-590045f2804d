import java.io.File;

public class VerifyCompilation {
    public static void main(String[] args) {
        // Check if the compiled classes exist
        File classesDir = new File("build/classes/com/parkingsystem");
        if (classesDir.exists() && classesDir.isDirectory()) {
            System.out.println("Compilation successful! Classes directory exists.");
            
            // Check for specific class files
            File mainClass = new File("build/classes/com/parkingsystem/Main.class");
            if (mainClass.exists()) {
                System.out.println("Main class exists.");
            } else {
                System.out.println("Main class does not exist.");
            }
            
            // List all compiled files
            System.out.println("\nCompiled files:");
            listFiles(classesDir, "");
        } else {
            System.out.println("Compilation failed! Classes directory does not exist.");
        }
    }
    
    private static void listFiles(File dir, String indent) {
        File[] files = dir.listFiles();
        if (files != null) {
            for (File file : files) {
                System.out.println(indent + file.getName());
                if (file.isDirectory()) {
                    listFiles(file, indent + "  ");
                }
            }
        }
    }
}
