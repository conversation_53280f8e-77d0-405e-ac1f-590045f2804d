package com.parkingsystem.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import com.parkingsystem.model.Vehicle;

/**
 * Manages file operations for data persistence
 */
public class FileManager {
    private static final String DATA_DIRECTORY = "data";
    private static final String PARKING_DATA_FILE = DATA_DIRECTORY + File.separator + "parking_data.ser";
    private static final String TRANSACTION_LOG_FILE = DATA_DIRECTORY + File.separator + "transactions.log";
    
    /**
     * Initialize the file manager by creating necessary directories
     */
    public static void initialize() {
        File dataDir = new File(DATA_DIRECTORY);
        if (!dataDir.exists()) {
            dataDir.mkdir();
        }
    }
    
    /**
     * Save parking data to file
     * 
     * @param parkingLotManager The parking lot manager to save
     * @return true if save successful, false otherwise
     */
    public static boolean saveParkingData(ParkingLotManager parkingLotManager) {
        initialize();
        try (ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(PARKING_DATA_FILE))) {
            oos.writeObject(parkingLotManager);
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Load parking data from file
     * 
     * @return The loaded parking lot manager, or null if loading fails
     */
    public static ParkingLotManager loadParkingData() {
        initialize();
        File file = new File(PARKING_DATA_FILE);
        if (!file.exists()) {
            return null;
        }
        
        try (ObjectInputStream ois = new ObjectInputStream(new FileInputStream(PARKING_DATA_FILE))) {
            return (ParkingLotManager) ois.readObject();
        } catch (IOException | ClassNotFoundException e) {
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * Log a vehicle entry transaction
     * 
     * @param vehicle The vehicle that entered
     */
    public static void logEntry(Vehicle vehicle) {
        logTransaction("ENTRY", vehicle, 0.0);
    }
    
    /**
     * Log a vehicle exit transaction
     *
     * @param vehicle The vehicle that exited
     * @param fee The fee charged
     */
    public static void logExit(Vehicle vehicle, double fee) {
        logTransaction("EXIT", vehicle, fee);
    }

    /**
     * Log an overstay violation
     *
     * @param vehicle The vehicle that overstayed
     * @param fine The fine amount
     */
    public static void logOverstayViolation(Vehicle vehicle, double fine) {
        logTransaction("OVERSTAY_VIOLATION", vehicle, fine);
    }
    
    /**
     * Log a transaction to the transaction log file
     * 
     * @param type The transaction type (ENTRY/EXIT)
     * @param vehicle The vehicle involved
     * @param fee The fee charged (for EXIT transactions)
     */
    private static void logTransaction(String type, Vehicle vehicle, double fee) {
        initialize();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String timestamp = LocalDateTime.now().format(formatter);
        String logEntry = String.format("%s | %s | %s | %s | %.2f\n", 
                timestamp, type, vehicle.getVehicleNumber(), vehicle.getVehicleType(), fee);
        
        try {
            File file = new File(TRANSACTION_LOG_FILE);
            if (!file.exists()) {
                file.createNewFile();
            }
            
            FileOutputStream fos = new FileOutputStream(file, true);
            fos.write(logEntry.getBytes());
            fos.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    
    /**
     * Get all transactions from the log file
     * 
     * @return List of transaction log entries
     */
    public static List<String> getTransactionLogs() {
        initialize();
        List<String> logs = new ArrayList<>();
        File file = new File(TRANSACTION_LOG_FILE);
        
        if (!file.exists()) {
            return logs;
        }
        
        try {
            java.util.Scanner scanner = new java.util.Scanner(file);
            while (scanner.hasNextLine()) {
                logs.add(scanner.nextLine());
            }
            scanner.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        
        return logs;
    }
}
