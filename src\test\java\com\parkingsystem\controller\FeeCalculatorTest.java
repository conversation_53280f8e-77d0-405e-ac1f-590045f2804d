package com.parkingsystem.controller;

import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDateTime;

import org.junit.jupiter.api.Test;

import com.parkingsystem.model.Constants;
import com.parkingsystem.model.Vehicle;

/**
 * Unit tests for the FeeCalculator class
 */
public class FeeCalculatorTest {

    @Test
    public void testCalculateFeeForTwoWheelerLessThanOneHour() {
        // Create a vehicle
        Vehicle vehicle = new Vehicle("TW123", Constants.TWO_WHEELER);
        
        // Set entry time to 30 minutes ago
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime entryTime = now.minusMinutes(30);
        
        // Use reflection to set the entry time (since it's set to now in the constructor)
        try {
            java.lang.reflect.Field field = Vehicle.class.getDeclaredField("entryTime");
            field.setAccessible(true);
            field.set(vehicle, entryTime);
        } catch (Exception e) {
            fail("Failed to set entry time: " + e.getMessage());
        }
        
        // Set exit time to now
        vehicle.setExitTime(now);
        
        // Calculate fee
        double fee = FeeCalculator.calculateFee(vehicle);
        
        // Assert that the fee is the minimum fee for two-wheelers
        assertEquals(Constants.TWO_WHEELER_MIN_FEE, fee, 0.01);
    }
    
    @Test
    public void testCalculateFeeForTwoWheelerMoreThanOneHour() {
        // Create a vehicle
        Vehicle vehicle = new Vehicle("TW456", Constants.TWO_WHEELER);
        
        // Set entry time to 2.5 hours ago
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime entryTime = now.minusMinutes(150);
        
        // Use reflection to set the entry time
        try {
            java.lang.reflect.Field field = Vehicle.class.getDeclaredField("entryTime");
            field.setAccessible(true);
            field.set(vehicle, entryTime);
        } catch (Exception e) {
            fail("Failed to set entry time: " + e.getMessage());
        }
        
        // Set exit time to now
        vehicle.setExitTime(now);
        
        // Calculate fee
        double fee = FeeCalculator.calculateFee(vehicle);
        
        // Assert that the fee is for 3 hours (ceiling of 2.5)
        assertEquals(3 * Constants.TWO_WHEELER_RATE, fee, 0.01);
    }
    
    @Test
    public void testCalculateFeeForFourWheelerLessThanOneHour() {
        // Create a vehicle
        Vehicle vehicle = new Vehicle("FW123", Constants.FOUR_WHEELER);
        
        // Set entry time to 30 minutes ago
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime entryTime = now.minusMinutes(30);
        
        // Use reflection to set the entry time
        try {
            java.lang.reflect.Field field = Vehicle.class.getDeclaredField("entryTime");
            field.setAccessible(true);
            field.set(vehicle, entryTime);
        } catch (Exception e) {
            fail("Failed to set entry time: " + e.getMessage());
        }
        
        // Set exit time to now
        vehicle.setExitTime(now);
        
        // Calculate fee
        double fee = FeeCalculator.calculateFee(vehicle);
        
        // Assert that the fee is the minimum fee for four-wheelers
        assertEquals(Constants.FOUR_WHEELER_MIN_FEE, fee, 0.01);
    }
    
    @Test
    public void testCalculateFeeForFourWheelerMoreThanOneHour() {
        // Create a vehicle
        Vehicle vehicle = new Vehicle("FW456", Constants.FOUR_WHEELER);
        
        // Set entry time to 2.5 hours ago
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime entryTime = now.minusMinutes(150);
        
        // Use reflection to set the entry time
        try {
            java.lang.reflect.Field field = Vehicle.class.getDeclaredField("entryTime");
            field.setAccessible(true);
            field.set(vehicle, entryTime);
        } catch (Exception e) {
            fail("Failed to set entry time: " + e.getMessage());
        }
        
        // Set exit time to now
        vehicle.setExitTime(now);
        
        // Calculate fee
        double fee = FeeCalculator.calculateFee(vehicle);
        
        // Assert that the fee is for 3 hours (ceiling of 2.5)
        assertEquals(3 * Constants.FOUR_WHEELER_RATE, fee, 0.01);
    }
    
    @Test
    public void testFormatDuration() {
        // Create a vehicle
        Vehicle vehicle = new Vehicle("TW789", Constants.TWO_WHEELER);
        
        // Set entry time to 2 hours and 30 minutes ago
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime entryTime = now.minusHours(2).minusMinutes(30);
        
        // Use reflection to set the entry time
        try {
            java.lang.reflect.Field field = Vehicle.class.getDeclaredField("entryTime");
            field.setAccessible(true);
            field.set(vehicle, entryTime);
        } catch (Exception e) {
            fail("Failed to set entry time: " + e.getMessage());
        }
        
        // Set exit time to now
        vehicle.setExitTime(now);
        
        // Format duration
        String duration = FeeCalculator.formatDuration(vehicle);
        
        // Assert that the duration is formatted correctly
        assertEquals("2 hours 30 minutes", duration);
    }
}
