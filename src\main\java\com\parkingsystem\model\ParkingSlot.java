package com.parkingsystem.model;

import java.io.Serializable;

/**
 * Represents a parking slot in the parking system
 */
public class ParkingSlot implements Serializable {
    private static final long serialVersionUID = 1L;
    private int slotNumber;
    private String slotType;
    private boolean isOccupied;
    private Vehicle parkedVehicle;

    /**
     * Constructor for creating a new parking slot
     *
     * @param slotNumber The slot number
     * @param slotType The type of slot (Two Wheeler/Four Wheeler)
     */
    public ParkingSlot(int slotNumber, String slotType) {
        this.slotNumber = slotNumber;
        this.slotType = slotType;
        this.isOccupied = false;
        this.parkedVehicle = null;
    }

    /**
     * Get the slot number
     *
     * @return The slot number
     */
    public int getSlotNumber() {
        return slotNumber;
    }

    /**
     * Get the slot type
     *
     * @return The slot type (Two Wheeler/Four Wheeler)
     */
    public String getSlotType() {
        return slotType;
    }

    /**
     * Check if the slot is occupied
     *
     * @return true if occupied, false otherwise
     */
    public boolean isOccupied() {
        return isOccupied;
    }

    /**
     * Park a vehicle in this slot
     *
     * @param vehicle The vehicle to park
     * @return true if parking successful, false if slot already occupied
     */
    public boolean parkVehicle(Vehicle vehicle) {
        if (!isOccupied) {
            this.parkedVehicle = vehicle;
            this.isOccupied = true;
            vehicle.setSlotNumber(this.slotNumber);
            return true;
        }
        return false;
    }

    /**
     * Remove the vehicle from this slot
     *
     * @return The vehicle that was parked, or null if no vehicle was parked
     */
    public Vehicle removeVehicle() {
        if (isOccupied) {
            Vehicle vehicle = this.parkedVehicle;
            this.parkedVehicle = null;
            this.isOccupied = false;
            return vehicle;
        }
        return null;
    }

    /**
     * Get the vehicle parked in this slot
     *
     * @return The parked vehicle, or null if no vehicle is parked
     */
    public Vehicle getParkedVehicle() {
        return parkedVehicle;
    }

    @Override
    public String toString() {
        return "ParkingSlot [slotNumber=" + slotNumber + ", slotType=" + slotType + ", isOccupied=" + isOccupied + "]";
    }
}
