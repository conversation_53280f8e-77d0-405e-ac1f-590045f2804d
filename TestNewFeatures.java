import com.parkingsystem.model.Vehicle;
import com.parkingsystem.model.Constants;
import com.parkingsystem.controller.FeeCalculator;
import java.time.LocalDateTime;

/**
 * Simple test to verify the new time-slot-based pricing system
 */
public class TestNewFeatures {
    public static void main(String[] args) {
        System.out.println("Testing New Time-Slot-Based Pricing System");
        System.out.println("==========================================");
        
        // Test 1: Vehicle with time slot (no overtime)
        System.out.println("\nTest 1: Two-wheeler with time slot (no overtime)");
        Vehicle vehicle1 = new Vehicle("TW001", Constants.TWO_WHEELER, "06:00-10:00");
        // Set entry time to 7 AM and exit time to 9 AM (within slot)
        LocalDateTime entryTime1 = LocalDateTime.now().withHour(7).withMinute(0).withSecond(0);
        LocalDateTime exitTime1 = entryTime1.plusHours(2); // Exit at 9 AM
        try {
            java.lang.reflect.Field field = Vehicle.class.getDeclaredField("entryTime");
            field.setAccessible(true);
            field.set(vehicle1, entryTime1);
        } catch (Exception e) {
            System.out.println("Error setting entry time: " + e.getMessage());
        }
        vehicle1.setExitTime(exitTime1);
        double fee1 = FeeCalculator.calculateFee(vehicle1);
        double overtime1 = FeeCalculator.getOvertimeFine(vehicle1);
        System.out.println("Vehicle: " + vehicle1.getVehicleNumber());
        System.out.println("Type: " + vehicle1.getVehicleType());
        System.out.println("Time Slot: " + vehicle1.getSelectedTimeSlot());
        System.out.println("Entry: " + entryTime1.toLocalTime() + ", Exit: " + exitTime1.toLocalTime());
        System.out.println("Total Fee: ₹" + fee1);
        System.out.println("Overtime Fine: ₹" + overtime1);
        System.out.println("Expected: ₹" + Constants.TWO_WHEELER_SLOT_RATE + " (no overtime)");
        
        // Test 2: Vehicle with time slot (with overtime)
        System.out.println("\nTest 2: Four-wheeler with time slot (with overtime)");
        Vehicle vehicle2 = new Vehicle("FW001", Constants.FOUR_WHEELER, "14:00-18:00");
        // Set entry time to 2 PM and exit time to 7 PM (exceeds 6 PM slot end)
        LocalDateTime entryTime2 = LocalDateTime.now().withHour(14).withMinute(0).withSecond(0);
        LocalDateTime exitTime2 = entryTime2.plusHours(5); // Exit at 7 PM (1 hour overtime)
        try {
            java.lang.reflect.Field field = Vehicle.class.getDeclaredField("entryTime");
            field.setAccessible(true);
            field.set(vehicle2, entryTime2);
        } catch (Exception e) {
            System.out.println("Error setting entry time: " + e.getMessage());
        }
        vehicle2.setExitTime(exitTime2);
        double fee2 = FeeCalculator.calculateFee(vehicle2);
        double overtime2 = FeeCalculator.getOvertimeFine(vehicle2);
        System.out.println("Vehicle: " + vehicle2.getVehicleNumber());
        System.out.println("Type: " + vehicle2.getVehicleType());
        System.out.println("Time Slot: " + vehicle2.getSelectedTimeSlot());
        System.out.println("Entry: " + entryTime2.toLocalTime() + ", Exit: " + exitTime2.toLocalTime());
        System.out.println("Total Fee: ₹" + fee2);
        System.out.println("Overtime Fine: ₹" + overtime2);
        System.out.println("Expected: ₹" + (Constants.FOUR_WHEELER_SLOT_RATE + Constants.OVERTIME_FINE) + " (with overtime)");
        
        // Test 3: Legacy vehicle (no time slot)
        System.out.println("\nTest 3: Legacy vehicle (no time slot)");
        Vehicle vehicle3 = new Vehicle("LG001", Constants.TWO_WHEELER);
        vehicle3.setExitTime(LocalDateTime.now().plusHours(3)); // Exit after 3 hours
        double fee3 = FeeCalculator.calculateFee(vehicle3);
        double overstay3 = FeeCalculator.calculateOverstayFine(vehicle3);
        System.out.println("Vehicle: " + vehicle3.getVehicleNumber());
        System.out.println("Type: " + vehicle3.getVehicleType());
        System.out.println("Time Slot: " + (vehicle3.getSelectedTimeSlot() != null ? vehicle3.getSelectedTimeSlot() : "None (Legacy)"));
        System.out.println("Total Fee: ₹" + fee3);
        System.out.println("Overstay Fine: ₹" + overstay3);
        System.out.println("Expected: ₹" + (3 * Constants.TWO_WHEELER_RATE) + " (legacy pricing)");
        
        // Test 4: Display available time slots
        System.out.println("\nTest 4: Available Time Slots");
        System.out.println("Defined time slots:");
        for (String slot : Constants.TIME_SLOTS) {
            System.out.println("- " + slot);
        }
        
        // Test 5: Pricing constants
        System.out.println("\nTest 5: Pricing Constants (in Rupees)");
        System.out.println("Two-wheeler slot rate: ₹" + Constants.TWO_WHEELER_SLOT_RATE);
        System.out.println("Four-wheeler slot rate: ₹" + Constants.FOUR_WHEELER_SLOT_RATE);
        System.out.println("Overtime fine: ₹" + Constants.OVERTIME_FINE);
        System.out.println("Parking closes at: " + Constants.PARKING_CLOSE_HOUR + ":00 (10 PM)");
        
        System.out.println("\n==========================================");
        System.out.println("Test completed successfully!");
    }
}
